#!/bin/bash

# Cloudflare 完整部署脚本
# 用于部署前端应用、邮件处理器和 API Worker

set -e

echo "🚀 开始部署 Cloudflare 应用..."

# 检查 wrangler 是否已安装
if ! command -v wrangler &> /dev/null; then
    echo "❌ 错误: wrangler 未安装"
    echo "请运行: npm install -g wrangler"
    exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
    echo "❌ 错误: 未登录 Cloudflare"
    echo "请运行: wrangler login"
    exit 1
fi

# 构建前端应用
echo "🏗️  构建前端应用..."
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到 package.json 文件"
    exit 1
fi

npm run build

echo "🌐 部署前端到 Cloudflare Pages..."
# 部署前端到 Cloudflare Pages
wrangler pages deploy dist --project-name tempmail-app

echo "🔗 部署 API Worker..."
# 部署 API Worker
wrangler deploy --config wrangler.toml

echo "✅ 所有应用部署完成!"

echo ""
echo "📋 部署信息:"
echo "- 前端应用: tempmail-app (Cloudflare Pages)"
echo "- API Worker: ofun-email-unified (Worker)"
echo ""
echo "🌐 访问地址:"
echo "- 前端应用: https://tempmail-app-6o8.pages.dev"
echo "- API 端点: https://ofun-email-unified.your-subdomain.workers.dev"
echo ""
echo "🔧 下一步配置:"
echo "1. 在 Cloudflare 控制台中配置邮件路由"
echo "2. 设置 catch-all 规则指向 ofun-email-handler"
echo "3. 确保 DNS 记录正确配置"
echo "4. 配置前端环境变量连接后端 API"
echo "5. 测试邮件接收功能"
