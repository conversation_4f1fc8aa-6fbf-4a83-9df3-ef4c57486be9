# Cloudflare Workers 统一配置文件
# 合并了 API 服务和邮件处理器

name = "ofun-email-unified"
main = "workers/src/index.js"
compatibility_date = "2024-01-01"

# D1 数据库配置
[[d1_databases]]
binding = "DB"
database_name = "ofun-email-db"
database_id = "25f4e059-5ad7-46a2-8e23-8f9cf55baded"

# 环境变量
[vars]
ENVIRONMENT = "production"
ADMIN_PASSWORD = "your-admin-password-here"

# GitHub OAuth 配置（需要在 Cloudflare Dashboard 中设置为 secrets）
GITHUB_CLIENT_ID = "********************"
GITHUB_CLIENT_SECRET = "c3a6bc53c55b1da20f11183bfc76b2641e11796b"

# 可选：Webhook 通知 URL
# WEBHOOK_URL = "https://your-webhook-endpoint.com/notify"

# 日志分析配置
[observability]
enabled = true
head_sampling_rate = 1
