// Cloudflare Workers 主入口文件
import { handleAPIRequest } from './handlers/api.js';
import { handleEmailProcessing } from './handlers/email.js';

export default {
  // HTTP 请求处理（API 功能）
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    // CORS 头设置
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // 处理 OPTIONS 请求
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      // 路由处理 - API 请求
      if (path.startsWith('/api/')) {
        return await handleAPIRequest(request, env, corsHeaders);
      }

      // 默认返回 404
      return new Response('Not found', { status: 404, headers: corsHeaders });
    } catch (error) {
      console.error('Request Error:', error);
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Internal server error' 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
  },

  // 邮件处理功能
  async email(message, env, ctx) {
    return await handleEmailProcessing(message, env);
  }
};
