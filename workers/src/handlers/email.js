// 邮件处理器
import PostalMime from 'postal-mime';

export async function handleEmailProcessing(message, env) {
  try {
    console.log('Processing email from:', message.from, 'to:', message.to);

    // 使用 postal-mime 解析邮件
    const parsedEmail = await parseEmailWithPostalMime(message.raw);
    
    // 提取收件人邮箱地址
    const toEmail = message.to;
    console.log('Recipient email:', toEmail);

    // 查找对应的邮箱记录
    const emailRecord = await env.DB.prepare(
      'SELECT * FROM emails WHERE email = ?'
    ).bind(toEmail).first();

    if (!emailRecord) {
      console.log('Email record not found for:', toEmail);
      return;
    }

    // 提取邮件内容
    const subject = parsedEmail.subject || '无主题';
    const textContent = parsedEmail.text || '';
    const htmlContent = parsedEmail.html || '';
    
    // 提取发件人信息
    const fromAddress = parsedEmail.from?.address || message.from;
    const fromName = parsedEmail.from?.name || '';

    // 尝试提取验证码
    const verificationCode = extractVerificationCode(textContent + ' ' + htmlContent);

    // 保存邮件到数据库
    await env.DB.prepare(`
      INSERT INTO mails (
        email_id, 
        from_address, 
        from_name, 
        subject, 
        text_content, 
        html_content, 
        verification_code,
        received_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `).bind(
      emailRecord.id,
      fromAddress,
      fromName,
      subject,
      textContent,
      htmlContent,
      verificationCode
    ).run();

    console.log('Email saved successfully for:', toEmail);

  } catch (error) {
    console.error('Email processing error:', error);
  }
}

// 使用 postal-mime 解析邮件
async function parseEmailWithPostalMime(rawEmail) {
  try {
    const parser = new PostalMime();
    const email = await parser.parse(rawEmail);
    return email;
  } catch (error) {
    console.error('Email parsing error:', error);
    return {
      subject: '解析失败',
      text: '邮件解析失败',
      html: '',
      from: { address: '', name: '' }
    };
  }
}

// 提取验证码的函数
function extractVerificationCode(content) {
  // 常见的验证码模式
  const patterns = [
    /验证码[：:\s]*([0-9]{4,8})/i,
    /verification code[：:\s]*([0-9]{4,8})/i,
    /code[：:\s]*([0-9]{4,8})/i,
    /pin[：:\s]*([0-9]{4,8})/i,
    /\b([0-9]{4,8})\b.*验证/i,
    /\b([0-9]{4,8})\b.*code/i,
    /您的验证码是[：:\s]*([0-9]{4,8})/i,
    /your verification code is[：:\s]*([0-9]{4,8})/i,
    /\b([0-9]{6})\b/g // 6位数字（最常见的验证码格式）
  ];

  for (const pattern of patterns) {
    const match = content.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}
