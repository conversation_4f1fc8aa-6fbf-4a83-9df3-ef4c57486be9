// 邮箱相关 API 处理器
import { getUserFromRequest } from '../utils/auth.js';

export async function handleEmailAPI(request, env, corsHeaders, path) {
  switch (path) {
    case '/emails':
      return handleGetEmails(request, env, corsHeaders);
    case '/emails/count':
      return handleGetEmailCount(request, env, corsHeaders);
    case '/generate':
      return handleGenerateEmail(request, env, corsHeaders);
    case '/delete':
      return handleDeleteEmails(request, env, corsHeaders);
    default:
      if (path.startsWith('/emails/')) {
        return handleGetEmailById(request, env, corsHeaders, path);
      }
      return new Response(JSON.stringify({
        success: false,
        message: 'Email API endpoint not found'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
  }
}

async function handleGetEmails(request, env, corsHeaders) {
  // 邮箱列表查询实现
  return new Response(JSON.stringify({
    success: false,
    message: '邮箱列表功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleGetEmailCount(request, env, corsHeaders) {
  // 邮箱数量查询实现
  return new Response(JSON.stringify({
    success: false,
    message: '邮箱数量查询功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleGenerateEmail(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    // 验证用户身份
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 检查用户配额
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    if (!credits || credits.balance < 1) {
      return new Response(JSON.stringify({
        success: false,
        message: '配额不足'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { domain, customName } = await request.json();
    
    if (!domain) {
      return new Response(JSON.stringify({
        success: false,
        message: '请选择域名'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 生成邮箱名称
    const emailName = customName || generateRandomEmailName();
    const fullEmail = `${emailName}@${domain}`;
    const secret = generateRandomSecret();

    // 保存到数据库
    const insertResult = await env.DB.prepare(`
      INSERT INTO emails (email, secret, domain, user_id, created_at, expires_at)
      VALUES (?, ?, ?, ?, datetime('now'), datetime('now', '+30 days'))
    `).bind(fullEmail, secret, domain, user.id).run();

    // 扣除配额
    await env.DB.prepare(
      'UPDATE user_credits SET balance = balance - 1, total_spent = total_spent + 1, updated_at = datetime("now") WHERE user_id = ?'
    ).bind(user.id).run();

    // 记录交易
    await env.DB.prepare(`
      INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, created_at)
      VALUES (?, 'spend', 1, ?, '生成临时邮箱', datetime('now'))
    `).bind(user.id, credits.balance - 1).run();

    return new Response(JSON.stringify({
      success: true,
      email: {
        id: insertResult.meta.last_row_id,
        fullEmail: fullEmail,
        secret: secret,
        domain: domain,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      },
      creditsRemaining: credits.balance - 1
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Generate email error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '生成邮箱失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleDeleteEmails(request, env, corsHeaders) {
  // 删除邮箱实现
  return new Response(JSON.stringify({
    success: false,
    message: '删除邮箱功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleGetEmailById(request, env, corsHeaders, path) {
  // 根据ID获取邮箱实现
  return new Response(JSON.stringify({
    success: false,
    message: '获取邮箱详情功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 生成随机邮箱名称
function generateRandomEmailName() {
  const adjectives = ['quick', 'bright', 'cool', 'smart', 'fast', 'nice', 'good', 'best'];
  const nouns = ['mail', 'box', 'user', 'temp', 'test', 'demo', 'sample'];
  const numbers = Math.floor(Math.random() * 9999);
  
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  
  return `${adjective}${noun}${numbers}`;
}

// 生成随机密钥
function generateRandomSecret() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
