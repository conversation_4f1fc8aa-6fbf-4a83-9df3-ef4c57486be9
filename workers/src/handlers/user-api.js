// 用户相关 API 处理器
import { getUserFromRequest } from '../utils/auth.js';

export async function handleUserAPI(request, env, corsHeaders, path) {
  switch (path) {
    case '/user/profile':
      return handleUserProfile(request, env, corsHeaders);
    case '/user/credits':
      return handleUserCredits(request, env, corsHeaders);
    case '/user/transactions':
      return handleUserTransactions(request, env, corsHeaders);
    case '/user/generate-email':
      return handleUserGenerateEmail(request, env, corsHeaders);
    case '/redemption/redeem':
      return handleRedeemCode(request, env, corsHeaders);
    default:
      return new Response(JSON.stringify({
        success: false,
        message: 'User API endpoint not found'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
  }
}

async function handleUserProfile(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取用户配额信息
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    return new Response(JSON.stringify({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.display_name,
        avatarUrl: user.avatar_url,
        emailVerified: user.email_verified,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at
      },
      credits: credits ? {
        balance: credits.balance,
        totalEarned: credits.total_earned,
        totalSpent: credits.total_spent
      } : null
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get user profile error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取用户信息失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleUserCredits(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '用户配额功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleUserTransactions(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '用户交易记录功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleUserGenerateEmail(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '用户生成邮箱功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleRedeemCode(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const user = await getUserFromRequest(request, env);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '未授权访问'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { code } = await request.json();
    
    if (!code) {
      return new Response(JSON.stringify({
        success: false,
        message: '请输入兑换码'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 查找兑换码
    const redemptionCode = await env.DB.prepare(
      'SELECT * FROM redemption_codes WHERE code = ? AND is_active = 1 AND (expires_at IS NULL OR expires_at > datetime("now"))'
    ).bind(code).first();

    if (!redemptionCode) {
      return new Response(JSON.stringify({
        success: false,
        message: '兑换码无效或已过期'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 检查使用次数限制
    if (redemptionCode.max_uses > 0) {
      const usageCount = await env.DB.prepare(
        'SELECT COUNT(*) as count FROM redemption_code_usage WHERE code_id = ?'
      ).bind(redemptionCode.id).first();

      if (usageCount.count >= redemptionCode.max_uses) {
        return new Response(JSON.stringify({
          success: false,
          message: '兑换码使用次数已达上限'
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
    }

    // 检查用户是否已使用过此兑换码
    const userUsage = await env.DB.prepare(
      'SELECT * FROM redemption_code_usage WHERE code_id = ? AND user_id = ?'
    ).bind(redemptionCode.id, user.id).first();

    if (userUsage) {
      return new Response(JSON.stringify({
        success: false,
        message: '您已使用过此兑换码'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取当前配额
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    const currentBalance = credits ? credits.balance : 0;
    const newBalance = currentBalance + redemptionCode.credits;

    // 更新用户配额
    if (credits) {
      await env.DB.prepare(
        'UPDATE user_credits SET balance = ?, total_earned = total_earned + ?, updated_at = datetime("now") WHERE user_id = ?'
      ).bind(newBalance, redemptionCode.credits, user.id).run();
    } else {
      await env.DB.prepare(
        'INSERT INTO user_credits (user_id, balance, total_earned, total_spent, created_at, updated_at) VALUES (?, ?, ?, 0, datetime("now"), datetime("now"))'
      ).bind(user.id, newBalance, redemptionCode.credits).run();
    }

    // 记录兑换码使用
    await env.DB.prepare(
      'INSERT INTO redemption_code_usage (code_id, user_id, used_at) VALUES (?, ?, datetime("now"))'
    ).bind(redemptionCode.id, user.id).run();

    // 记录交易
    await env.DB.prepare(
      'INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, created_at) VALUES (?, "earn", ?, ?, ?, datetime("now"))'
    ).bind(user.id, redemptionCode.credits, newBalance, `兑换码: ${code}`).run();

    return new Response(JSON.stringify({
      success: true,
      message: '兑换成功',
      creditsAwarded: redemptionCode.credits,
      newBalance: newBalance
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Redeem code error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '兑换失败，请稍后重试'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}
