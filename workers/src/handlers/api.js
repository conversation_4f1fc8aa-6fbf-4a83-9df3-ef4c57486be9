// API 请求处理器
import { handleEmailAPI } from './email-api.js';
import { handleAuthAPI } from './auth-api.js';
import { handleUserAPI } from './user-api.js';
import { handleAdminAPI } from './admin-api.js';

export async function handleAPIRequest(request, env, corsHeaders) {
  const url = new URL(request.url);
  const path = url.pathname;

  // 移除 /api 前缀
  const apiPath = path.replace('/api', '');

  // 邮箱相关 API
  if (apiPath.startsWith('/emails') || apiPath.startsWith('/generate') || apiPath.startsWith('/delete')) {
    return await handleEmailAPI(request, env, corsHeaders, apiPath);
  }

  // 认证相关 API
  if (apiPath.startsWith('/auth')) {
    return await handleAuthAPI(request, env, corsHeaders, apiPath);
  }

  // 用户相关 API
  if (apiPath.startsWith('/user') || apiPath.startsWith('/redemption')) {
    return await handleUserAPI(request, env, corsHeaders, apiPath);
  }

  // 管理员相关 API
  if (apiPath.startsWith('/admin')) {
    return await handleAdminAPI(request, env, corsHeaders, apiPath);
  }

  // 未知路由
  return new Response(JSON.stringify({
    success: false,
    message: 'API endpoint not found'
  }), {
    status: 404,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}
