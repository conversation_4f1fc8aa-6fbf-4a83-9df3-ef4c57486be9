// 管理员相关 API 处理器

export async function handleAdminAPI(request, env, corsHeaders, path) {
  switch (path) {
    case '/admin/login':
      return handleAdminLogin(request, env, corsHeaders);
    case '/admin/emails':
      return handleAdminEmails(request, env, corsHeaders);
    case '/admin/users':
      return handleAdminUsers(request, env, corsHeaders);
    case '/admin/redemption-codes':
      return handleAdminRedemptionCodes(request, env, corsHeaders);
    case '/admin/announcements':
      return handleAdminAnnouncements(request, env, corsHeaders);
    default:
      return new Response(JSON.stringify({
        success: false,
        message: 'Admin API endpoint not found'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
  }
}

// 管理员登录
async function handleAdminLogin(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  const { username, password } = await request.json();

  // 查询管理员
  const admin = await env.DB.prepare(
    'SELECT * FROM admins WHERE username = ?'
  ).bind(username).first();

  if (!admin) {
    return new Response(JSON.stringify({
      success: false,
      message: '用户名或密码错误'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 简单密码验证（实际项目中应该使用 bcrypt）
  if (password !== 'admin123') {
    return new Response(JSON.stringify({
      success: false,
      message: '用户名或密码错误'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 生成简单的 token（实际项目中应该使用 JWT）
  const token = btoa(`${username}:${Date.now()}`);

  return new Response(JSON.stringify({
    success: true,
    token: token,
    message: '登录成功'
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 获取邮箱列表（管理员）
async function handleAdminEmails(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  // 验证 token
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      message: '未授权访问'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // 获取邮箱列表
    const emails = await env.DB.prepare(`
      SELECT e.*, u.email as user_email, u.username,
             (SELECT COUNT(*) FROM mails WHERE email_id = e.id) as mail_count
      FROM emails e
      LEFT JOIN users u ON e.user_id = u.id
      ORDER BY e.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(limit, offset).all();

    // 获取总数
    const totalResult = await env.DB.prepare('SELECT COUNT(*) as total FROM emails').first();
    const total = totalResult.total;

    return new Response(JSON.stringify({
      success: true,
      emails: emails.results,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Admin get emails error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '获取邮箱列表失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 其他管理员功能的占位符
async function handleAdminUsers(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '管理员用户管理功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleAdminRedemptionCodes(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '管理员兑换码管理功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleAdminAnnouncements(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '管理员公告管理功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}
