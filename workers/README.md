# TempMail Workers 模块化重构

## 项目结构

```
workers/
├── src/
│   ├── index.js              # 主入口文件
│   ├── handlers/
│   │   ├── api.js            # API 路由分发器
│   │   ├── auth-api.js       # 认证相关 API
│   │   ├── email-api.js      # 邮箱相关 API
│   │   ├── user-api.js       # 用户相关 API
│   │   ├── admin-api.js      # 管理员相关 API
│   │   └── email.js          # 邮件处理器
│   └── utils/
│       └── auth.js           # 认证工具函数
├── unified.js                # 原始单文件版本（保留）
├── wrangler.toml     # 模块化版本配置
└── README.md                 # 本文件
```

## 模块说明

### 主入口 (src/index.js)
- 处理 HTTP 请求和邮件接收
- 设置 CORS 头
- 路由分发到相应的处理器

### API 处理器 (src/handlers/)

#### api.js
- API 路由的主分发器
- 根据路径前缀分发到具体的处理器

#### auth-api.js
- 用户注册、登录、登出
- GitHub OAuth 认证
- JWT 令牌管理
- 邮箱验证码功能

#### email-api.js
- 临时邮箱生成
- 邮箱列表查询
- 邮箱删除管理

#### user-api.js
- 用户资料管理
- 配额查询和管理
- 兑换码功能
- 交易记录

#### admin-api.js
- 管理员登录
- 邮箱管理
- 用户管理
- 系统设置

#### email.js
- 邮件接收和解析
- 验证码提取
- 邮件存储

### 工具函数 (src/utils/)

#### auth.js
- JWT 生成和验证
- 密码哈希和验证
- 用户身份验证
- 随机字符串生成

## 部署说明

### 开发环境
```bash
# 使用模块化版本
wrangler dev -c wrangler-modular.toml

# 使用原始版本
wrangler dev -c wrangler.toml
```

### 生产环境
```bash
# 部署模块化版本
wrangler deploy -c wrangler-modular.toml --env production

# 部署原始版本
wrangler deploy -c wrangler.toml --env production
```

## 配置说明

### 环境变量
- `JWT_SECRET`: JWT 签名密钥
- `GITHUB_CLIENT_ID`: GitHub OAuth 客户端 ID
- `GITHUB_CLIENT_SECRET`: GitHub OAuth 客户端密钥
- `GITHUB_REDIRECT_URI`: GitHub OAuth 回调地址

### 数据库
- 使用 Cloudflare D1 数据库
- 需要在 wrangler.toml 中配置数据库 ID

### 邮件路由
- 配置邮件接收域名
- 所有邮件将路由到 email 处理器

## 优势

1. **模块化**: 代码按功能分离，易于维护
2. **可扩展**: 新功能可以独立开发和测试
3. **可读性**: 每个文件职责单一，代码更清晰
4. **可测试**: 模块化便于单元测试
5. **团队协作**: 不同开发者可以并行开发不同模块

## 迁移说明

1. 原始的 `unified.js` 文件保留，确保向后兼容
2. 新的模块化版本使用独立的配置文件
3. 可以逐步迁移功能到模块化版本
4. 数据库结构保持不变

## 注意事项

1. Cloudflare Workers 对 ES6 模块的支持有限
2. 某些功能可能需要进一步完善
3. 生产环境部署前需要充分测试
4. 建议先在开发环境验证所有功能
