// Cloudflare Workers 统一处理器
// 合并了 API 服务和邮件处理功能

import PostalMime from 'postal-mime';

export default {
  // HTTP 请求处理（API 功能）
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    // CORS 头设置
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // 处理 OPTIONS 请求
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      // 路由处理 - API 请求
      if (path.startsWith('/api/')) {
        return await handleAPIRequest(request, env, corsHeaders);
      }

      // 默认返回 404
      return new Response('Not found', { status: 404, headers: corsHeaders });
    } catch (error) {
      console.error('Request Error:', error);
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Internal server error' 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
  },

  // 邮件处理功能
  async email(message, env, ctx) {
    try {
      console.log('Processing email from:', message.from, 'to:', message.to);

      // 使用 postal-mime 解析邮件
      const parsedEmail = await parseEmailWithPostalMime(message.raw);

      console.log('Parsed email subject:', parsedEmail.subject);
      console.log('Parsed email text length:', parsedEmail.text?.length || 0);
      console.log('Parsed email html length:', parsedEmail.html?.length || 0);

      // 提取收件人邮箱地址
      const toEmail = message.to.toLowerCase();

      // 查找对应的邮箱记录
      const emailRecord = await findEmailByAddress(env.DB, toEmail);

      if (!emailRecord) {
        console.log(`No email record found for: ${toEmail}`);
        return;
      }

      // 检查邮箱是否过期
      const now = new Date();
      const expireDate = new Date(emailRecord.expire_at);

      if (now > expireDate) {
        console.log(`Email expired for: ${toEmail}`);
        return;
      }

      // 生成唯一的消息ID
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 存储邮件到数据库
      const result = await env.DB.prepare(`
        INSERT INTO mail_messages (
          email_id,
          message_id,
          from_address,
          to_address,
          subject,
          text_content,
          html_content
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).bind(
        emailRecord.id,
        messageId,
        message.from,
        toEmail,
        parsedEmail.subject || '',
        parsedEmail.text || '',
        parsedEmail.html || ''
      ).run();

      if (result.success) {
        console.log(`Email stored successfully: ${messageId}`);

        // 发送新邮件通知（可选：如果配置了 webhook）
        await sendNewEmailNotification(env, {
          emailId: emailRecord.id,
          messageId: messageId,
          from: message.from,
          to: toEmail,
          subject: parsedEmail.subject || '',
          receivedAt: new Date().toISOString()
        });
      } else {
        console.error(`Failed to store email: ${messageId}`);
      }

    } catch (error) {
      console.error('Email processing error:', error);
      console.error('Error stack:', error.stack);
    }
  }
};

// API 请求处理函数
async function handleAPIRequest(request, env, corsHeaders) {
  const url = new URL(request.url);
  const apiPath = url.pathname.replace('/api', '');
  
  switch (apiPath) {
    case '/admin/login':
      return handleAdminLogin(request, env, corsHeaders);
    
    case '/admin/emails':
      return handleAdminEmails(request, env, corsHeaders);
    
    case '/admin/emails/batch':
      return handleBatchGenerate(request, env, corsHeaders);

    case '/admin/emails/custom':
      return handleCustomGenerate(request, env, corsHeaders);

    case '/admin/emails/batch-delete':
      return handleBatchDeleteEmails(request, env, corsHeaders);

    case '/email-query':
      return handleEmailQuery(request, env, corsHeaders);

    case '/emails':
      return handleGetEmails(request, env, corsHeaders);

    case '/email-detail':
      return handleEmailDetail(request, env, corsHeaders);

    case '/emails/batch-delete':
      return handleBatchDeleteMails(request, env, corsHeaders);

    case '/emails/count':
      return handleGetEmailCount(request, env, corsHeaders);

    // 用户认证相关
    case '/auth/register':
      return handleUserRegister(request, env, corsHeaders);

    case '/auth/login':
      return handleUserLogin(request, env, corsHeaders);

    case '/auth/logout':
      return handleUserLogout(request, env, corsHeaders);

    case '/auth/refresh':
      return handleRefreshToken(request, env, corsHeaders);

    case '/auth/github':
      return handleGitHubOAuth(request, env, corsHeaders);

    case '/auth/github/callback':
      return handleGitHubCallback(request, env, corsHeaders);

    // 用户相关
    case '/user/profile':
      return handleUserProfile(request, env, corsHeaders);

    case '/user/credits':
      return handleUserCredits(request, env, corsHeaders);

    case '/user/transactions':
      return handleUserTransactions(request, env, corsHeaders);

    case '/user/generate-email':
      return handleUserGenerateEmail(request, env, corsHeaders);

    // 兑换码相关
    case '/redemption/redeem':
      return handleRedeemCode(request, env, corsHeaders);

    // 系统公告
    case '/announcements':
      return handleGetAnnouncements(request, env, corsHeaders);

    // 管理员用户管理
    case '/admin/users':
      return handleAdminUsers(request, env, corsHeaders);

    case '/admin/users/credits':
      return handleAdminUserCredits(request, env, corsHeaders);

    // 管理员兑换码管理
    case '/admin/redemption-codes':
      return handleAdminRedemptionCodes(request, env, corsHeaders);

    case '/admin/redemption-codes/generate':
      return handleGenerateRedemptionCodes(request, env, corsHeaders);

    // 管理员系统公告管理
    case '/admin/announcements':
      return handleAdminAnnouncements(request, env, corsHeaders);

    default:
      if (apiPath.startsWith('/admin/emails/') && request.method === 'DELETE') {
        return handleDeleteEmail(request, env, corsHeaders, apiPath);
      }
      if (apiPath.startsWith('/admin/emails/') && request.method === 'PUT') {
        return handleUpdateEmail(request, env, corsHeaders, apiPath);
      }
      return new Response('API endpoint not found', {
        status: 404,
        headers: corsHeaders
      });
  }
}

// 邮件处理相关函数

// 根据邮箱地址查找邮箱记录
async function findEmailByAddress(db, emailAddress) {
  try {
    // 分离邮箱名和域名
    const [mailPart, domainPart] = emailAddress.split('@');
    
    if (!mailPart || !domainPart) {
      return null;
    }
    
    // 查询数据库
    const result = await db.prepare(
      'SELECT * FROM emails WHERE mail = ? AND domain = ?'
    ).bind(mailPart, domainPart).first();
    
    return result;
  } catch (error) {
    console.error('Error finding email by address:', error);
    return null;
  }
}

// 发送新邮件通知
async function sendNewEmailNotification(env, emailData) {
  try {
    // 如果配置了 webhook URL，发送通知
    if (env.WEBHOOK_URL) {
      const webhookPayload = {
        type: 'new_email',
        timestamp: new Date().toISOString(),
        data: emailData
      };

      await fetch(env.WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Cloudflare-Email-Handler/1.0'
        },
        body: JSON.stringify(webhookPayload)
      });

      console.log(`Webhook notification sent for email: ${emailData.messageId}`);
    }
  } catch (error) {
    console.error('Failed to send notification:', error);
  }
}

// 使用 postal-mime 解析邮件
async function parseEmailWithPostalMime(rawStream) {
  try {
    console.log('🔍 Starting postal-mime email parsing...');

    // 读取 ReadableStream 为 Uint8Array
    const rawBytes = await streamToUint8Array(rawStream);
    console.log('📧 Raw email bytes length:', rawBytes.length);

    // 使用 postal-mime 解析邮件
    const parser = new PostalMime();
    const parsed = await parser.parse(rawBytes);

    console.log('✅ Email parsed successfully with postal-mime');
    console.log('📋 Subject:', parsed.subject);
    console.log('📝 Text length:', parsed.text?.length || 0);
    console.log('🌐 HTML length:', parsed.html?.length || 0);
    console.log('📎 Attachments:', parsed.attachments?.length || 0);

    return {
      subject: parsed.subject || '',
      from: parsed.from?.text || '',
      to: parsed.to?.text || '',
      date: parsed.date || '',
      text: parsed.text || '',
      html: parsed.html || '',
      headers: parsed.headers || {},
      attachments: parsed.attachments || []
    };
  } catch (error) {
    console.error('❌ Postal-mime parsing error:', error);
    throw error;
  }
}

// 将 ReadableStream 转换为 Uint8Array
async function streamToUint8Array(stream) {
  try {
    const response = new Response(stream);
    const arrayBuffer = await response.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    console.log('📦 Stream read successfully, bytes length:', uint8Array.length);
    return uint8Array;
  } catch (error) {
    console.warn('⚠️ Response.arrayBuffer() failed, falling back to manual reading:', error);

    const reader = stream.getReader();
    const chunks = [];

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }

      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const combined = new Uint8Array(totalLength);
      let offset = 0;

      for (const chunk of chunks) {
        combined.set(chunk, offset);
        offset += chunk.length;
      }

      console.log('📦 Manual stream read successful, bytes length:', combined.length);
      return combined;
    } finally {
      reader.releaseLock();
    }
  }
}

// ==================== API 处理函数 ====================

// 管理员登录
async function handleAdminLogin(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  const { username, password } = await request.json();

  // 查询管理员
  const admin = await env.DB.prepare(
    'SELECT * FROM admins WHERE username = ?'
  ).bind(username).first();

  if (!admin) {
    return new Response(JSON.stringify({
      success: false,
      message: '用户名或密码错误'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 简单密码验证（实际项目中应该使用 bcrypt）
  if (password !== 'admin123') {
    return new Response(JSON.stringify({
      success: false,
      message: '用户名或密码错误'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 生成简单的 token（实际项目中应该使用 JWT）
  const token = btoa(`${username}:${Date.now()}`);

  return new Response(JSON.stringify({
    success: true,
    token: token,
    message: '登录成功'
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 获取邮箱列表（管理员）
async function handleAdminEmails(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  // 验证 token
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      message: '未授权访问'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1');
  const limit = parseInt(url.searchParams.get('limit') || '20');
  const offset = (page - 1) * limit;

  // 获取邮箱列表
  const emails = await env.DB.prepare(
    'SELECT * FROM emails ORDER BY created_at DESC LIMIT ? OFFSET ?'
  ).bind(limit, offset).all();

  // 获取总数
  const total = await env.DB.prepare(
    'SELECT COUNT(*) as count FROM emails'
  ).first();

  return new Response(JSON.stringify({
    success: true,
    emails: emails.results.map(email => ({
      id: email.id,
      mail: email.mail,
      domain: email.domain,
      type: email.type,
      relation: email.relation,
      remark: email.remark || '',
      secret: email.secret,
      createdAt: email.created_at,
      updatedAt: email.updated_at,
      expire: email.expire_at
    })),
    total: total.count
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 批量生成邮箱
async function handleBatchGenerate(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  // 验证 token
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      message: '未授权访问'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  const { count, domain, type, relation = '', remark = '', expireDays, prefix = '' } = await request.json();

  // 验证必需参数（relation是可选的）
  if (!count || !domain || !type || !expireDays) {
    return new Response(JSON.stringify({
      success: false,
      message: '参数不完整，缺少必需参数：count, domain, type, expireDays'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 验证参数类型和范围
  if (typeof count !== 'number' || count < 1 || count > 100) {
    return new Response(JSON.stringify({
      success: false,
      message: '生成数量必须是1-100之间的数字'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  if (typeof expireDays !== 'number' || expireDays < 1 || expireDays > 365) {
    return new Response(JSON.stringify({
      success: false,
      message: '有效期必须是1-365之间的数字'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  const emails = [];
  const expireDate = new Date();
  expireDate.setDate(expireDate.getDate() + expireDays);

  for (let i = 0; i < count; i++) {
    const mail = generateEmailName(prefix);
    const secret = `${Math.random().toString(36).substring(2, 6)}-${Math.random().toString(36).substring(2, 10)}-${Math.random().toString(36).substring(2, 8)}`;

    const result = await env.DB.prepare(
      'INSERT INTO emails (mail, domain, type, relation, remark, secret, expire_at) VALUES (?, ?, ?, ?, ?, ?, ?)'
    ).bind(mail, domain, type, relation, remark, secret, expireDate.toISOString()).run();

    if (result.success) {
      emails.push({
        id: result.meta.last_row_id,
        mail,
        domain,
        type,
        relation: relation,
        remark: remark,
        secret,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        expire: expireDate.toISOString()
      });
    }
  }

  return new Response(JSON.stringify({
    success: true,
    emails: emails,
    message: `成功生成 ${emails.length} 个邮箱`
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 生成指定名称的邮箱（管理员）
async function handleCustomGenerate(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  // 验证 token
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      message: '未授权访问'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  const { customName, domain, type, relation = '', remark = '', expireDays } = await request.json();

  // 验证必需参数
  if (!customName || !domain || !type || !expireDays) {
    return new Response(JSON.stringify({
      success: false,
      message: '参数不完整，缺少必需参数：customName, domain, type, expireDays'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 验证邮箱名称格式
  if (!/^[a-z][a-z0-9_]{7,14}$/.test(customName)) {
    return new Response(JSON.stringify({
      success: false,
      message: '邮箱名称必须以小写字母开头，只能包含小写字母、数字、下划线，长度8-15位'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 验证下划线数量（最多一个）
  const underscoreCount = (customName.match(/_/g) || []).length;
  if (underscoreCount > 1) {
    return new Response(JSON.stringify({
      success: false,
      message: '邮箱名称中下划线最多只能有一个'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 检查邮箱是否已存在
  const existingEmail = await env.DB.prepare(
    'SELECT id FROM emails WHERE mail = ? AND domain = ?'
  ).bind(customName, domain).first();

  if (existingEmail) {
    return new Response(JSON.stringify({
      success: false,
      message: `邮箱 ${customName}@${domain} 已存在`
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 计算过期时间
  const expireDate = new Date();
  expireDate.setDate(expireDate.getDate() + expireDays);

  // 生成授权密钥
  const secret = `${Math.random().toString(36).substring(2, 6)}-${Math.random().toString(36).substring(2, 10)}-${Math.random().toString(36).substring(2, 8)}`;

  try {
    // 插入数据库
    const result = await env.DB.prepare(
      'INSERT INTO emails (mail, domain, type, relation, remark, secret, expire_at) VALUES (?, ?, ?, ?, ?, ?, ?)'
    ).bind(customName, domain, type, relation, remark, secret, expireDate.toISOString()).run();

    if (result.success) {
      const emailData = {
        id: result.meta.last_row_id,
        mail: customName,
        domain,
        type,
        relation: relation,
        remark: remark,
        secret,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        expire: expireDate.toISOString()
      };

      return new Response(JSON.stringify({
        success: true,
        email: emailData,
        message: `成功生成邮箱 ${customName}@${domain}`
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    } else {
      throw new Error('数据库插入失败');
    }
  } catch (error) {
    console.error('Custom generate error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '生成邮箱失败，请稍后重试'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 通过授权密钥查询邮箱
async function handleEmailQuery(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  const { secret } = await request.json();

  if (!secret) {
    return new Response(JSON.stringify({
      success: false,
      message: '授权密钥不能为空'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 查询邮箱
  const email = await env.DB.prepare(
    'SELECT * FROM emails WHERE secret = ?'
  ).bind(secret).first();

  if (!email) {
    return new Response(JSON.stringify({
      success: false,
      message: '授权密钥不存在'
    }), {
      status: 404,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 检查是否过期
  const now = new Date();
  const expireDate = new Date(email.expire_at);

  if (now > expireDate) {
    return new Response(JSON.stringify({
      success: false,
      message: '邮箱已过期',
      expired: true
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  return new Response(JSON.stringify({
    success: true,
    emailData: {
      createdAt: email.created_at,
      domain: email.domain,
      expire: email.expire_at,
      mail: email.mail,
      secret: email.secret,
      updatedAt: email.updated_at
    }
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 获取邮件列表
async function handleGetEmails(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  const { secret, email } = await request.json();

  // 验证授权密钥
  const emailData = await env.DB.prepare(
    'SELECT * FROM emails WHERE secret = ?'
  ).bind(secret).first();

  if (!emailData) {
    return new Response(JSON.stringify({
      success: false,
      message: '授权密钥无效'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 获取邮件列表
  const mails = await env.DB.prepare(
    'SELECT * FROM mail_messages WHERE email_id = ? ORDER BY received_at DESC'
  ).bind(emailData.id).all();

  return new Response(JSON.stringify({
    success: true,
    emails: mails.results.map(mail => ({
      id: mail.message_id,
      from: mail.from_address,
      to: mail.to_address,
      subject: mail.subject,
      text: mail.text_content,
      html: mail.html_content,
      date: mail.received_at
    }))
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json; charset=utf-8' }
  });
}

// 获取邮件详情
async function handleEmailDetail(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  const { secret, email, mailId } = await request.json();

  // 验证授权密钥
  const emailData = await env.DB.prepare(
    'SELECT * FROM emails WHERE secret = ?'
  ).bind(secret).first();

  if (!emailData) {
    return new Response(JSON.stringify({
      success: false,
      message: '授权密钥无效'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 获取邮件详情
  const mail = await env.DB.prepare(
    'SELECT * FROM mail_messages WHERE message_id = ? AND email_id = ?'
  ).bind(mailId, emailData.id).first();

  if (!mail) {
    return new Response(JSON.stringify({
      success: false,
      message: '邮件不存在'
    }), {
      status: 404,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  return new Response(JSON.stringify({
    success: true,
    email: {
      id: mail.message_id,
      from: mail.from_address,
      to: mail.to_address,
      subject: mail.subject,
      text: mail.text_content,
      html: mail.html_content,
      date: mail.received_at
    }
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json; charset=utf-8' }
  });
}

// 删除邮箱
async function handleDeleteEmail(request, env, corsHeaders, apiPath) {
  // 验证 token
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      message: '未授权访问'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  const emailId = apiPath.split('/').pop();

  const result = await env.DB.prepare(
    'DELETE FROM emails WHERE id = ?'
  ).bind(emailId).run();

  if (result.success) {
    return new Response(JSON.stringify({
      success: true,
      message: '邮箱删除成功'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } else {
    return new Response(JSON.stringify({
      success: false,
      message: '删除失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 批量删除邮箱
async function handleBatchDeleteEmails(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  // 验证 token
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      message: '未授权访问'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  const { emailIds } = await request.json();

  if (!emailIds || !Array.isArray(emailIds) || emailIds.length === 0) {
    return new Response(JSON.stringify({
      success: false,
      message: '请选择要删除的邮箱'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  try {
    // 构建批量删除的 SQL
    const placeholders = emailIds.map(() => '?').join(',');
    const result = await env.DB.prepare(
      `DELETE FROM emails WHERE id IN (${placeholders})`
    ).bind(...emailIds).run();

    return new Response(JSON.stringify({
      success: true,
      message: `成功删除 ${emailIds.length} 个邮箱`,
      deletedCount: emailIds.length
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      message: '批量删除失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 更新邮箱信息
async function handleUpdateEmail(request, env, corsHeaders, apiPath) {
  if (request.method !== 'PUT') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  // 验证 token
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      message: '未授权访问'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  const emailId = apiPath.split('/').pop();
  const { type, relation, remark, expireDays } = await request.json();

  try {
    // 如果提供了 expireDays，计算新的过期时间
    let updateFields = [];
    let updateValues = [];

    if (type !== undefined) {
      updateFields.push('type = ?');
      updateValues.push(type);
    }

    if (relation !== undefined) {
      updateFields.push('relation = ?');
      updateValues.push(relation);
    }

    if (remark !== undefined) {
      updateFields.push('remark = ?');
      updateValues.push(remark);
    }

    if (expireDays !== undefined) {
      const expireDate = new Date();
      expireDate.setDate(expireDate.getDate() + expireDays);
      updateFields.push('expire_at = ?');
      updateValues.push(expireDate.toISOString());
    }

    updateFields.push('updated_at = ?');
    updateValues.push(new Date().toISOString());
    updateValues.push(emailId);

    if (updateFields.length === 1) { // 只有 updated_at
      return new Response(JSON.stringify({
        success: false,
        message: '没有提供要更新的字段'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const result = await env.DB.prepare(
      `UPDATE emails SET ${updateFields.join(', ')} WHERE id = ?`
    ).bind(...updateValues).run();

    if (result.success) {
      return new Response(JSON.stringify({
        success: true,
        message: '邮箱信息更新成功'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    } else {
      return new Response(JSON.stringify({
        success: false,
        message: '更新失败'
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      message: '更新失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 批量删除邮件
async function handleBatchDeleteMails(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  const { secret, mailIds } = await request.json();

  if (!secret) {
    return new Response(JSON.stringify({
      success: false,
      message: '授权密钥不能为空'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  if (!mailIds || !Array.isArray(mailIds) || mailIds.length === 0) {
    return new Response(JSON.stringify({
      success: false,
      message: '请选择要删除的邮件'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 验证授权密钥
  const emailData = await env.DB.prepare(
    'SELECT * FROM emails WHERE secret = ?'
  ).bind(secret).first();

  if (!emailData) {
    return new Response(JSON.stringify({
      success: false,
      message: '授权密钥无效'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  try {
    // 构建批量删除的 SQL，确保只删除属于当前邮箱的邮件
    const placeholders = mailIds.map(() => '?').join(',');
    const result = await env.DB.prepare(
      `DELETE FROM mail_messages WHERE message_id IN (${placeholders}) AND email_id = ?`
    ).bind(...mailIds, emailData.id).run();

    return new Response(JSON.stringify({
      success: true,
      message: `成功删除 ${result.changes || mailIds.length} 封邮件`,
      deletedCount: result.changes || mailIds.length
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Batch delete mails error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '批量删除邮件失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 获取邮件数量
async function handleGetEmailCount(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  const { secret } = await request.json();

  // 验证授权密钥
  const emailData = await env.DB.prepare(
    'SELECT * FROM emails WHERE secret = ?'
  ).bind(secret).first();

  if (!emailData) {
    return new Response(JSON.stringify({
      success: false,
      message: '授权密钥无效'
    }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 获取邮件数量
  const countResult = await env.DB.prepare(
    'SELECT COUNT(*) as count FROM mail_messages WHERE email_id = ?'
  ).bind(emailData.id).first();

  return new Response(JSON.stringify({
    success: true,
    count: countResult.count || 0
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// ==================== 工具函数 ====================

// 生成邮件名称
function generateEmailName(prefix = '') {
  // 字符集：小写英文字母、数字
  const letters = 'abcdefghijklmnopqrstuvwxyz0123456789';

  // 验证前缀长度
  if (prefix && prefix.length > 8) {
    prefix = prefix.substring(0, 8);
  }

  // 计算需要生成的随机部分长度
  const prefixLength = prefix.length;
  const minLength = 8;
  const maxLength = 15;

  // 随机选择总长度（8-15位）
  const totalLength = Math.floor(Math.random() * (maxLength - minLength + 1)) + minLength;
  const randomLength = totalLength - prefixLength;

  // 如果前缀已经达到或超过最小长度，直接返回前缀
  if (randomLength <= 0) {
    return prefix || generateRandomString(8, letters);
  }

  // 生成随机部分
  let randomPart = '';
  let underscoreUsed = false;

  for (let i = 0; i < randomLength; i++) {
    let char;

    // 决定是否使用下划线（最多一个，且不在开头结尾）
    if (!underscoreUsed && i > 0 && i < randomLength - 1 && Math.random() < 0.1) {
      char = '_';
      underscoreUsed = true;
    } else {
      // 使用小写字母或数字
      char = letters[Math.floor(Math.random() * letters.length)];
    }

    randomPart += char;
  }

  // 确保首位是字母
  let result = prefix + randomPart;
  if (!/^[a-z]/.test(result)) {
    // 如果首位不是字母，替换为随机字母
    const firstChar = 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
    result = firstChar + result.substring(1);
  }

  return result;
}

// 生成指定长度的随机字符串（不含下划线）
function generateRandomString(length, charset) {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset[Math.floor(Math.random() * charset.length)];
  }
  return result;
}

// 简单的密码哈希函数（实际项目中应该使用更安全的方法如 bcrypt）
async function hashPassword(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password + 'salt_string_here'); // 添加盐值
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// 验证密码
async function verifyPassword(password, hash) {
  const passwordHash = await hashPassword(password);
  return passwordHash === hash;
}

// 验证用户会话
async function verifyUserSession(request, env) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);

  const session = await env.DB.prepare(
    `SELECT s.*, u.* FROM user_sessions s
     JOIN users u ON s.user_id = u.id
     WHERE s.session_token = ? AND s.is_active = TRUE AND s.expires_at > CURRENT_TIMESTAMP AND u.status = 'active'`
  ).bind(token).first();

  if (session) {
    // 更新最后使用时间
    await env.DB.prepare(
      'UPDATE user_sessions SET last_used_at = CURRENT_TIMESTAMP WHERE id = ?'
    ).bind(session.id).run();
  }

  return session;
}

// ==================== 基础 API 函数（占位符） ====================
// 注意：以下函数是占位符，需要根据原始 email-api.js 的完整实现来补充

async function handleUserRegister(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const { email, password, username, verificationCode } = await request.json();

    if (!email || !password) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱和密码不能为空'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱格式不正确'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证密码长度
    if (password.length < 6) {
      return new Response(JSON.stringify({
        success: false,
        message: '密码长度至少6位'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 检查邮箱是否已存在
    const existingUser = await env.DB.prepare(
      'SELECT id FROM users WHERE email = ?'
    ).bind(email).first();

    if (existingUser) {
      return new Response(JSON.stringify({
        success: false,
        message: '该邮箱已被注册'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 如果提供了验证码，验证验证码
    if (verificationCode) {
      const verification = await env.DB.prepare(
        'SELECT * FROM email_verifications WHERE email = ? AND code = ? AND expires_at > datetime("now") AND used = 0'
      ).bind(email, verificationCode).first();

      if (!verification) {
        return new Response(JSON.stringify({
          success: false,
          message: '验证码无效或已过期'
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      // 标记验证码为已使用
      await env.DB.prepare(
        'UPDATE email_verifications SET used = 1 WHERE id = ?'
      ).bind(verification.id).run();

      // 创建用户（已验证邮箱）
      const hashedPassword = await hashPassword(password);
      const insertResult = await env.DB.prepare(`
        INSERT INTO users (email, username, password_hash, email_verified, created_at)
        VALUES (?, ?, ?, 1, datetime('now'))
      `).bind(email, username || null, hashedPassword).run();

      const userId = insertResult.meta.last_row_id;

      // 为新用户创建配额记录
      await env.DB.prepare(`
        INSERT INTO user_credits (user_id, balance, total_earned, total_spent, created_at, updated_at)
        VALUES (?, 5, 5, 0, datetime('now'), datetime('now'))
      `).bind(userId).run();

      // 记录配额获得交易
      await env.DB.prepare(`
        INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, created_at)
        VALUES (?, 'earn', 5, 5, '新用户注册奖励', datetime('now'))
      `).bind(userId).run();

      return new Response(JSON.stringify({
        success: true,
        message: '注册成功！'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    } else {
      // 发送验证码
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

      // 保存验证码到数据库
      await env.DB.prepare(`
        INSERT INTO email_verifications (email, code, expires_at, created_at)
        VALUES (?, ?, datetime('now', '+10 minutes'), datetime('now'))
      `).bind(email, verificationCode).run();

      // 发送验证邮件（这里简化处理，实际应该发送邮件）
      // 在实际环境中，你需要集成邮件服务如 SendGrid, AWS SES 等
      console.log(`Verification code for ${email}: ${verificationCode}`);

      return new Response(JSON.stringify({
        success: true,
        message: '验证码已发送到您的邮箱，请查收',
        requiresVerification: true
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('User register error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '注册失败，请稍后重试'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleUserLogin(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱和密码不能为空'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 查找用户
    const user = await env.DB.prepare(
      'SELECT * FROM users WHERE email = ?'
    ).bind(email).first();

    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱或密码错误'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证密码
    const isValidPassword = await verifyPassword(password, user.password_hash);
    if (!isValidPassword) {
      return new Response(JSON.stringify({
        success: false,
        message: '邮箱或密码错误'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 检查邮箱是否已验证
    if (!user.email_verified) {
      return new Response(JSON.stringify({
        success: false,
        message: '请先验证您的邮箱'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 更新最后登录时间
    await env.DB.prepare(
      'UPDATE users SET last_login_at = datetime("now") WHERE id = ?'
    ).bind(user.id).run();

    // 生成 JWT 令牌
    const token = await generateJWT({
      userId: user.id,
      email: user.email,
      exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7天
    }, env.JWT_SECRET || 'default-secret');

    const refreshToken = await generateJWT({
      userId: user.id,
      type: 'refresh',
      exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30天
    }, env.JWT_SECRET || 'default-secret');

    // 获取用户配额信息
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    return new Response(JSON.stringify({
      success: true,
      token: token,
      refreshToken: refreshToken,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.display_name,
        avatarUrl: user.avatar_url,
        emailVerified: user.email_verified,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at
      },
      credits: credits ? {
        balance: credits.balance,
        totalEarned: credits.total_earned,
        totalSpent: credits.total_spent
      } : null
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('User login error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '登录失败，请稍后重试'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleUserLogout(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '用户登出功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleRefreshToken(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '刷新令牌功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleGitHubOAuth(request, env, corsHeaders) {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    // GitHub OAuth 配置
    const clientId = env.GITHUB_CLIENT_ID;
    const redirectUri = env.GITHUB_REDIRECT_URI || 'https://your-domain.com/api/auth/github/callback';

    if (!clientId) {
      return new Response(JSON.stringify({
        success: false,
        message: 'GitHub OAuth 未配置'
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 生成随机 state
    const state = crypto.randomUUID();

    // 构建 GitHub OAuth URL
    const authUrl = new URL('https://github.com/login/oauth/authorize');
    authUrl.searchParams.set('client_id', clientId);
    authUrl.searchParams.set('redirect_uri', redirectUri);
    authUrl.searchParams.set('scope', 'user:email');
    authUrl.searchParams.set('state', state);

    return new Response(JSON.stringify({
      success: true,
      authUrl: authUrl.toString(),
      state: state
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('GitHub OAuth error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: 'GitHub OAuth 初始化失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleGitHubCallback(request, env, corsHeaders) {
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405, headers: corsHeaders });
  }

  try {
    const { code, state } = await request.json();

    if (!code || !state) {
      return new Response(JSON.stringify({
        success: false,
        message: '缺少必要参数'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // GitHub OAuth 配置
    const clientId = env.GITHUB_CLIENT_ID;
    const clientSecret = env.GITHUB_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      return new Response(JSON.stringify({
        success: false,
        message: 'GitHub OAuth 未配置'
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 1. 用授权码换取访问令牌
    const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_id: clientId,
        client_secret: clientSecret,
        code: code
      })
    });

    const tokenData = await tokenResponse.json();

    if (!tokenData.access_token) {
      return new Response(JSON.stringify({
        success: false,
        message: 'GitHub 授权失败'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 2. 获取用户信息
    const userResponse = await fetch('https://api.github.com/user', {
      headers: {
        'Authorization': `Bearer ${tokenData.access_token}`,
        'User-Agent': 'TempMail-App'
      }
    });

    const userData = await userResponse.json();

    if (!userData.id) {
      return new Response(JSON.stringify({
        success: false,
        message: '获取用户信息失败'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 3. 获取用户邮箱
    const emailResponse = await fetch('https://api.github.com/user/emails', {
      headers: {
        'Authorization': `Bearer ${tokenData.access_token}`,
        'User-Agent': 'TempMail-App'
      }
    });

    const emailData = await emailResponse.json();
    const primaryEmail = emailData.find(email => email.primary)?.email || userData.email;

    if (!primaryEmail) {
      return new Response(JSON.stringify({
        success: false,
        message: '无法获取邮箱地址'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 4. 查找或创建用户
    let user = await env.DB.prepare(
      'SELECT * FROM users WHERE email = ? OR github_id = ?'
    ).bind(primaryEmail, userData.id.toString()).first();

    if (!user) {
      // 创建新用户
      const insertResult = await env.DB.prepare(`
        INSERT INTO users (email, username, display_name, avatar_url, github_id, email_verified, created_at)
        VALUES (?, ?, ?, ?, ?, 1, datetime('now'))
      `).bind(
        primaryEmail,
        userData.login,
        userData.name || userData.login,
        userData.avatar_url,
        userData.id.toString()
      ).run();

      // 获取新创建的用户
      user = await env.DB.prepare(
        'SELECT * FROM users WHERE id = ?'
      ).bind(insertResult.meta.last_row_id).first();

      // 为新用户创建配额记录
      await env.DB.prepare(`
        INSERT INTO user_credits (user_id, balance, total_earned, total_spent, created_at, updated_at)
        VALUES (?, 5, 5, 0, datetime('now'), datetime('now'))
      `).bind(user.id).run();

      // 记录配额获得交易
      await env.DB.prepare(`
        INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, created_at)
        VALUES (?, 'earn', 5, 5, '新用户注册奖励', datetime('now'))
      `).bind(user.id).run();
    } else {
      // 更新现有用户的 GitHub 信息
      await env.DB.prepare(`
        UPDATE users SET
          github_id = ?,
          avatar_url = ?,
          display_name = ?,
          last_login_at = datetime('now')
        WHERE id = ?
      `).bind(
        userData.id.toString(),
        userData.avatar_url,
        userData.name || user.display_name,
        user.id
      ).run();
    }

    // 5. 生成 JWT 令牌
    const token = await generateJWT({
      userId: user.id,
      email: user.email,
      exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7天
    }, env.JWT_SECRET || 'default-secret');

    const refreshToken = await generateJWT({
      userId: user.id,
      type: 'refresh',
      exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30天
    }, env.JWT_SECRET || 'default-secret');

    // 6. 获取用户配额信息
    const credits = await env.DB.prepare(
      'SELECT * FROM user_credits WHERE user_id = ?'
    ).bind(user.id).first();

    return new Response(JSON.stringify({
      success: true,
      token: token,
      refreshToken: refreshToken,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.display_name,
        avatarUrl: user.avatar_url,
        emailVerified: user.email_verified,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at
      },
      credits: credits ? {
        balance: credits.balance,
        totalEarned: credits.total_earned,
        totalSpent: credits.total_spent
      } : null
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('GitHub callback error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: 'GitHub 登录处理失败'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function handleUserProfile(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '用户资料功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleUserCredits(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '用户积分功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleUserTransactions(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '用户交易记录功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleUserGenerateEmail(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '用户生成邮箱功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleRedeemCode(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '兑换码功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleGetAnnouncements(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '系统公告功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleAdminUsers(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '管理员用户管理功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleAdminUserCredits(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '管理员用户积分管理功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// JWT 辅助函数
async function generateJWT(payload, secret) {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const encodedHeader = btoa(JSON.stringify(header)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
  const encodedPayload = btoa(JSON.stringify(payload)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');

  const data = `${encodedHeader}.${encodedPayload}`;
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
  const encodedSignature = btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');

  return `${data}.${encodedSignature}`;
}

// 密码哈希函数
async function hashPassword(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password);
  const hash = await crypto.subtle.digest('SHA-256', data);
  return Array.from(new Uint8Array(hash))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

// 验证密码
async function verifyPassword(password, hash) {
  const hashedPassword = await hashPassword(password);
  return hashedPassword === hash;
}

async function handleAdminRedemptionCodes(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '管理员兑换码管理功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleGenerateRedemptionCodes(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '生成兑换码功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function handleAdminAnnouncements(request, env, corsHeaders) {
  return new Response(JSON.stringify({
    success: false,
    message: '管理员公告管理功能暂未实现'
  }), {
    status: 501,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}
