import { defineConfig, presetUno, presetIcons } from 'unocss';

export default defineConfig({
  presets: [
    presetUno(),
    presetIcons({
      collections: {
        carbon: () =>
          import('@iconify-json/carbon/icons.json').then(i => i.default),
        mdi: () => import('@iconify-json/mdi/icons.json').then(i => i.default),
        fa: () => import('@iconify-json/fa/icons.json').then(i => i.default),
        fas: () =>
          import('@iconify-json/fa-solid/icons.json').then(i => i.default),
      },
    }),
  ],
  theme: {},
  shortcuts: {},
});
