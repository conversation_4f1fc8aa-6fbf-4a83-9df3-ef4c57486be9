import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import 'dayjs/locale/zh-cn'

// 配置dayjs插件
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.locale('zh-cn')

/**
 * 格式化日期为中国时区显示
 * @param dateStr 日期字符串
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export const formatDateToChinaTime = (dateStr: string, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  try {
    // 如果是字符串，先解析为UTC时间，然后转换为中国时区
    let date = dayjs(dateStr)

    // 如果输入的是字符串且没有时区信息，假设为UTC时间
    if (typeof dateStr === 'string' && !dateStr.includes('+') && !dateStr.includes('Z')) {
      date = dayjs.utc(dateStr)
    }

    // 转换为中国时区 (UTC+8)
    return date.tz('Asia/Shanghai').format(format)
  } catch {
    return dateStr
  }
}

/**
 * 格式化日期为相对时间（如：2小时前）
 * @param dateStr 日期字符串
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (dateStr: string): string => {
  try {
    const now = dayjs().tz('Asia/Shanghai')
    let date = dayjs(dateStr)

    // 如果输入的是字符串且没有时区信息，假设为UTC时间
    if (typeof dateStr === 'string' && !dateStr.includes('+') && !dateStr.includes('Z')) {
      date = dayjs.utc(dateStr)
    }

    // 转换为中国时区
    date = date.tz('Asia/Shanghai')

    const diffInMinutes = now.diff(date, 'minute')

    if (diffInMinutes < 1) {
      return '刚刚'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`
    } else if (diffInMinutes < 1440) { // 24小时
      const hours = Math.floor(diffInMinutes / 60)
      return `${hours}小时前`
    } else if (diffInMinutes < 10080) { // 7天
      const days = Math.floor(diffInMinutes / 1440)
      return `${days}天前`
    } else {
      return date.format('MM-DD HH:mm')
    }
  } catch {
    return dateStr
  }
}

/**
 * 格式化过期时间（保持原有格式，不转换时区）
 * @param dateStr 日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatExpireTime = (dateStr: string): string => {
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch {
    return dateStr
  }
}
