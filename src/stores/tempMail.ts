import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { EmailService, type EmailData, type MailItem } from '../services/tempmail'
import { formatDateToChinaTime } from '../utils/dateFormat'

export const useTempMailStore = defineStore('tempMail', () => {
  // 状态
  const emailService = new EmailService()
  const currentEmailData = ref<EmailData | null>(null)
  const mails = ref<MailItem[]>([])
  const selectedMail = ref<MailItem | null>(null)
  const selectedMails = ref<MailItem[]>([])
  const loading = ref(false)
  const queryLoading = ref(false)
  const batchDeleteLoading = ref(false)
  const lastUpdateTime = ref('')
  const autoRefreshEnabled = ref(false)
  const autoRefreshInterval = ref(5) // 改为5秒
  const newMailsNotification = ref<{ count: number; timestamp: number } | null>(null)
  let autoRefreshTimer: number | null = null

  // 计算属性
  const currentEmailAddress = computed(() => {
    if (!currentEmailData.value) return ''
    return `${currentEmailData.value.mail}@${currentEmailData.value.domain}`
  })

  const isExpired = computed(() => {
    if (!currentEmailData.value) return true
    const expireTime = new Date(currentEmailData.value.expire)
    const now = new Date()
    return now > expireTime
  })

  // 方法
  const updateLastUpdateTime = () => {
    lastUpdateTime.value = formatDateToChinaTime(new Date().toISOString(), 'MM-DD HH:mm:ss')
  }

  const queryEmailBySecret = async (secretKey: string) => {
    if (!secretKey.trim()) {
      throw new Error('请输入授权密钥')
    }

    queryLoading.value = true
    try {
      const result = await emailService.queryBySecret(secretKey.trim())

      if (result.success && result.emailData) {
        // 清除之前的邮件数据
        mails.value = []
        selectedMail.value = null
        selectedMails.value = []
        lastUpdateTime.value = ''

        currentEmailData.value = result.emailData
        // 清除之前的邮件缓存
        mails.value = []
        return { success: true, message: '邮箱查询成功' }
      } else if (result.expired) {
        return { success: false, message: '邮箱已过期' }
      } else {
        return { success: false, message: result.message || '授权密钥不存在' }
      }
    } catch (error) {
      console.error('Failed to query email by secret:', error)
      return { success: false, message: '查询失败，请检查网络连接' }
    } finally {
      queryLoading.value = false
    }
  }

  const clearCurrentEmail = () => {
    currentEmailData.value = null
    mails.value = []
    selectedMail.value = null
    selectedMails.value = []
    lastUpdateTime.value = ''
    stopAutoRefresh()
    emailService.clearCurrentEmail()
  }

  const refreshMails = async () => {
    if (!currentEmailData.value) {
      throw new Error('请先查询邮箱')
    }

    if (isExpired.value) {
      throw new Error('邮箱已过期，无法接收邮件')
    }

    loading.value = true
    try {
      const mailList = await emailService.getMailList()
      mails.value = mailList
      selectedMails.value = [] // 重置选中状态
      updateLastUpdateTime()

      // 如果获取到邮件，停止自动刷新
      if (mailList.length > 0 && autoRefreshEnabled.value) {
        stopAutoRefresh()
      }

      return mailList.length
    } catch (error) {
      console.error('Failed to refresh mails:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const selectMail = async (mail: MailItem) => {
    try {
      const detail = await emailService.getMailDetail(mail.id)
      selectedMail.value = detail || mail
    } catch (error) {
      console.error('Failed to get mail detail:', error)
      selectedMail.value = mail
    }
  }

  const deleteMail = (mailId: string) => {
    mails.value = mails.value.filter((mail) => mail.id !== mailId)
    selectedMails.value = selectedMails.value.filter((mail) => mail.id !== mailId)
  }

  const clearMails = () => {
    mails.value = []
    selectedMail.value = null
    selectedMails.value = []
    lastUpdateTime.value = ''
  }

  const handleMailSelection = (mail: MailItem, checked: boolean) => {
    if (checked) {
      if (!selectedMails.value.some((m) => m.id === mail.id)) {
        selectedMails.value.push(mail)
      }
    } else {
      selectedMails.value = selectedMails.value.filter((m) => m.id !== mail.id)
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      selectedMails.value = [...mails.value]
    } else {
      selectedMails.value = []
    }
  }

  const batchDeleteMails = async () => {
    if (selectedMails.value.length === 0) {
      throw new Error('请选择要删除的邮件')
    }

    batchDeleteLoading.value = true
    try {
      const mailIds = selectedMails.value.map((mail) => mail.id)
      const result = await emailService.batchDeleteMails(mailIds)

      if (result.success) {
        // 从本地列表中移除已删除的邮件
        mails.value = mails.value.filter((mail) => !mailIds.includes(mail.id))
        selectedMails.value = []
        return { success: true, deletedCount: result.deletedCount || mailIds.length }
      } else {
        return { success: false, message: result.message || '批量删除失败' }
      }
    } catch (error) {
      console.error('Batch delete mails failed:', error)
      return { success: false, message: '批量删除失败，请检查网络连接' }
    } finally {
      batchDeleteLoading.value = false
    }
  }

  // 自动刷新相关
  const startAutoRefresh = () => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer)
    }

    autoRefreshTimer = setInterval(async () => {
      if (currentEmailData.value && !isExpired.value && !loading.value) {
        try {
          const currentCount = await emailService.getMailCount()
          if (currentCount !== mails.value.length) {
            const mailList = await emailService.getMailList()
            const newMailsCount = mailList.length - mails.value.length
            mails.value = mailList
            updateLastUpdateTime()

            if (newMailsCount > 0) {
              // 获取到邮件后停止自动刷新
              stopAutoRefresh()
              // 设置新邮件通知
              newMailsNotification.value = {
                count: newMailsCount,
                timestamp: Date.now()
              }
            }
          }
        } catch (error) {
          console.error('Auto refresh failed:', error)
        }
      }
    }, autoRefreshInterval.value * 1000)
  }

  const stopAutoRefresh = () => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer)
      autoRefreshTimer = null
    }
    autoRefreshEnabled.value = false
  }

  const toggleAutoRefresh = () => {
    autoRefreshEnabled.value = !autoRefreshEnabled.value

    if (autoRefreshEnabled.value) {
      if (!currentEmailData.value) {
        autoRefreshEnabled.value = false
        throw new Error('请先查询邮箱')
      }
      if (isExpired.value) {
        autoRefreshEnabled.value = false
        throw new Error('邮箱已过期，无法启用自动刷新')
      }
      startAutoRefresh()
      return { enabled: true, interval: autoRefreshInterval.value }
    } else {
      stopAutoRefresh()
      return { enabled: false }
    }
  }

  // 初始化数据 - 现在由 Pinia 持久化自动处理
  const initializeFromStorage = () => {
    // 如果有持久化的邮箱数据，重新初始化 emailService
    if (currentEmailData.value) {
      // 这里可以添加重新连接邮箱服务的逻辑
      console.log('Restored email data from persistence:', currentEmailData.value)
    }
  }

  return {
    // 状态
    currentEmailData,
    mails,
    selectedMail,
    selectedMails,
    loading,
    queryLoading,
    batchDeleteLoading,
    lastUpdateTime,
    autoRefreshEnabled,
    autoRefreshInterval,
    newMailsNotification,
    
    // 计算属性
    currentEmailAddress,
    isExpired,
    
    // 方法
    queryEmailBySecret,
    clearCurrentEmail,
    refreshMails,
    selectMail,
    deleteMail,
    clearMails,
    handleMailSelection,
    handleSelectAll,
    batchDeleteMails,
    toggleAutoRefresh,
    stopAutoRefresh,
    initializeFromStorage,
    updateLastUpdateTime,
    
    // 服务实例
    emailService
  }
}, {
  persist: {
    key: 'tempmail-store',
    storage: sessionStorage, // 使用 sessionStorage，关闭浏览器后自动清除
    pick: ['currentEmailData', 'mails', 'lastUpdateTime']
  }
})
