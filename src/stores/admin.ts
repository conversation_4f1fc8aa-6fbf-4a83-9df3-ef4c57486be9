import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAdminStore = defineStore('admin', () => {
  // 状态
  const isLoggedIn = ref(false)
  const authToken = ref('')

  // 计算属性
  const isAuthenticated = computed(() => isLoggedIn.value && !!authToken.value)

  // 方法
  const login = (token: string) => {
    isLoggedIn.value = true
    authToken.value = token
  }

  const logout = () => {
    isLoggedIn.value = false
    authToken.value = ''
  }

  return {
    // 状态
    isLoggedIn,
    authToken,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    logout
  }
}, {
  persist: {
    key: 'admin-store',
    storage: sessionStorage, // 使用 sessionStorage，关闭浏览器后自动清除
    pick: ['isLoggedIn', 'authToken']
  }
})
