<template>
  <div class="min-h-screen bg-white">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-3">
            <div class="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg">
              <i class="fas fa-envelope text-white text-lg"></i>
            </div>
            <div>
              <h1 class="text-xl font-bold text-gray-900">TempMail</h1>
              <p class="text-xs text-gray-500">临时邮箱服务</p>
            </div>
          </div>

          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/pricing" class="text-gray-600 hover:text-blue-600 transition-colors font-medium">
              配额购买
            </router-link>
            <router-link to="/apikey_mail" class="text-gray-600 hover:text-blue-600 transition-colors font-medium">
              密钥查询
            </router-link>

            <div v-if="userStore.isLoggedIn" class="flex items-center space-x-4">
              <div class="hidden sm:flex items-center space-x-2 px-3 py-1 bg-blue-50 rounded-full">
                <i class="fas fa-coins text-blue-600 text-sm"></i>
                <span class="text-sm font-medium text-blue-700">{{ userStore.credits?.balance || 0 }} 配额</span>
              </div>
              <el-dropdown @command="handleUserAction">
                <div class="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-lg transition-colors">
                  <el-avatar :size="32" :src="userStore.user?.avatarUrl">
                    <i class="fas fa-user"></i>
                  </el-avatar>
                  <span class="hidden sm:block text-sm font-medium text-gray-700">{{ userStore.user?.displayName || userStore.user?.username || '用户' }}</span>
                  <i class="fas fa-chevron-down text-xs text-gray-400"></i>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">
                      <i class="fas fa-user mr-2"></i>个人资料
                    </el-dropdown-item>
                    <el-dropdown-item command="logout" divided>
                      <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <div v-else class="flex items-center space-x-3">
              <router-link to="/login" class="text-gray-600 hover:text-blue-600 transition-colors font-medium">
                登录
              </router-link>
              <router-link to="/register" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium shadow-sm">
                注册
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 系统公告 -->
        <div v-if="announcements.length > 0" class="mb-12">
          <div v-for="announcement in announcements" :key="announcement.id"
               :class="[
                 'p-4 rounded-xl mb-4 shadow-sm',
                 announcement.type === 'success' ? 'bg-green-50 border border-green-200 text-green-800' :
                 announcement.type === 'warning' ? 'bg-yellow-50 border border-yellow-200 text-yellow-800' :
                 announcement.type === 'error' ? 'bg-red-50 border border-red-200 text-red-800' :
                 'bg-blue-50 border border-blue-200 text-blue-800'
               ]">
            <div class="flex items-start space-x-3">
              <i :class="[
                'mt-1',
                announcement.type === 'success' ? 'fas fa-check-circle text-green-500' :
                announcement.type === 'warning' ? 'fas fa-exclamation-triangle text-yellow-500' :
                announcement.type === 'error' ? 'fas fa-times-circle text-red-500' :
                'fas fa-info-circle text-blue-500'
              ]"></i>
              <div class="flex-1">
                <h3 class="font-semibold mb-1">{{ announcement.title }}</h3>
                <p class="text-sm opacity-90">{{ announcement.content }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Hero Content -->
        <div class="text-center mb-16">
          <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            专业的
            <span class="text-blue-600">临时邮箱</span>
            服务
          </h1>
          <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            保护您的隐私，避免垃圾邮件。快速生成临时邮箱地址，用于注册、验证和测试。
          </p>
          <div v-if="!userStore.isLoggedIn" class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 inline-block shadow-sm">
            <p class="text-blue-800 text-lg font-medium">
              <i class="fas fa-gift mr-2 text-blue-600"></i>
              新用户注册即可获得 <span class="font-bold text-blue-600">5个免费配额</span>
            </p>
            <p class="text-blue-600 text-sm mt-2">每个配额可生成一个临时邮箱，有效期30天</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 主要功能区域 -->
    <section class="py-16 bg-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- 邮箱生成表单 -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-12">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">生成临时邮箱</h2>
            <p class="text-gray-600">选择域名并自定义邮箱名称</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- 域名选择 -->
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-3">
                <i class="fas fa-globe mr-2 text-blue-600"></i>
                选择域名
              </label>
              <el-select
                v-model="selectedDomain"
                placeholder="请选择域名"
                class="w-full"
                size="large"
              >
                <el-option
                  v-for="domain in availableDomains"
                  :key="domain"
                  :label="domain"
                  :value="domain"
                />
              </el-select>
            </div>

            <!-- 自定义邮箱名 -->
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-3">
                <i class="fas fa-edit mr-2 text-blue-600"></i>
                邮箱名称（可选）
              </label>
              <el-input
                v-model="customName"
                placeholder="留空自动生成随机名称"
                :maxlength="20"
                show-word-limit
                size="large"
              />
            </div>
          </div>

          <!-- 生成按钮 -->
          <div class="mt-8 text-center">
            <el-button
              type="primary"
              size="large"
              :loading="generating"
              :disabled="!selectedDomain || !userStore.isLoggedIn || (userStore.credits?.balance || 0) < 1"
              @click="generateEmail"
              class="px-12 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all"
            >
              <i class="fas fa-plus mr-2"></i>
              生成临时邮箱 (消耗1配额)
            </el-button>

            <div v-if="!userStore.isLoggedIn" class="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
              <p class="text-blue-800 text-sm mb-3 font-medium">需要登录才能生成临时邮箱</p>
              <div class="flex justify-center space-x-4">
                <router-link to="/login" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                  立即登录
                </router-link>
                <router-link to="/register" class="bg-white text-blue-600 px-4 py-2 rounded-lg border border-blue-600 hover:bg-blue-50 transition-colors text-sm font-medium">
                  免费注册
                </router-link>
              </div>
            </div>

            <div v-else-if="(userStore.credits?.balance || 0) < 1" class="mt-6 p-4 bg-red-50 rounded-xl border border-red-200">
              <p class="text-red-800 text-sm mb-3 font-medium">配额不足，无法生成临时邮箱</p>
              <div class="text-center">
                <router-link to="/pricing" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm font-medium">
                  购买配额
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 生成结果区域 -->
    <section v-if="generatedEmail" class="py-16 bg-gray-50">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
          <div class="text-center mb-8">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-check-circle text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-2">
              临时邮箱生成成功
            </h3>
            <p class="text-gray-600">您的临时邮箱已准备就绪，有效期30天</p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 邮箱地址 -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
              <div class="flex items-center justify-between mb-4">
                <label class="text-sm font-semibold text-blue-800 flex items-center">
                  <i class="fas fa-envelope mr-2"></i>
                  邮箱地址
                </label>
                <el-button size="small" @click="copyEmail" type="primary" plain>
                  <i class="fas fa-copy mr-1"></i>复制
                </el-button>
              </div>
              <div class="text-lg font-mono bg-white p-4 rounded-lg border border-blue-200 break-all">
                {{ generatedEmail.fullEmail }}
              </div>
            </div>

            <!-- 授权密钥 -->
            <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-200">
              <div class="flex items-center justify-between mb-4">
                <label class="text-sm font-semibold text-purple-800 flex items-center">
                  <i class="fas fa-key mr-2"></i>
                  授权密钥
                </label>
                <el-button size="small" @click="copySecret" type="primary" plain>
                  <i class="fas fa-copy mr-1"></i>复制
                </el-button>
              </div>
              <div class="text-sm font-mono bg-white p-4 rounded-lg border border-purple-200 break-all">
                {{ generatedEmail.secret }}
              </div>
              <p class="text-xs text-purple-600 mt-3 flex items-center">
                <i class="fas fa-info-circle mr-1"></i>
                使用此密钥在"密钥查询"页面查看邮件
              </p>
            </div>
          </div>

          <div class="flex flex-col sm:flex-row justify-center gap-4">
            <router-link
              :to="{ path: '/apikey_mail', query: { secret: generatedEmail.secret } }"
              class="bg-blue-600 text-white px-8 py-3 rounded-xl hover:bg-blue-700 transition-colors font-semibold text-center shadow-lg hover:shadow-xl"
            >
              <i class="fas fa-envelope mr-2"></i>
              查看邮件
            </router-link>
            <el-button @click="generateAnother" size="large" class="px-8 py-3 rounded-xl font-semibold">
              <i class="fas fa-plus mr-2"></i>
              再生成一个
            </el-button>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能特性 -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">为什么选择我们？</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            专业的临时邮箱服务，为您的隐私和安全保驾护航
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
          <div class="text-center group">
            <div class="bg-gradient-to-br from-blue-100 to-blue-200 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
              <i class="fas fa-shield-alt text-blue-600 text-3xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">隐私保护</h3>
            <p class="text-gray-600 leading-relaxed">保护您的真实邮箱地址，避免垃圾邮件骚扰，确保个人信息安全</p>
          </div>

          <div class="text-center group">
            <div class="bg-gradient-to-br from-green-100 to-green-200 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
              <i class="fas fa-bolt text-green-600 text-3xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">即时接收</h3>
            <p class="text-gray-600 leading-relaxed">实时接收邮件，支持验证码自动提取，快速完成各种注册验证</p>
          </div>

          <div class="text-center group">
            <div class="bg-gradient-to-br from-purple-100 to-purple-200 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
              <i class="fas fa-mobile-alt text-purple-600 text-3xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">多端适配</h3>
            <p class="text-gray-600 leading-relaxed">完美支持桌面端和移动端访问，随时随地使用临时邮箱服务</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <div class="flex items-center justify-center space-x-3 mb-4">
            <div class="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg">
              <i class="fas fa-envelope text-white text-lg"></i>
            </div>
            <h3 class="text-xl font-bold">TempMail</h3>
          </div>
          <p class="text-gray-400 mb-6">专业的临时邮箱服务，保护您的隐私安全</p>
          <div class="flex justify-center space-x-6 text-sm text-gray-400">
            <span>© 2024 TempMail. All rights reserved.</span>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useClipboard } from '@vueuse/core'
import { useRouter } from 'vue-router'
import { useUserStore } from '../../stores/user'

const router = useRouter()
const userStore = useUserStore()
const { copy } = useClipboard()

// 响应式数据
const selectedDomain = ref('')
const customName = ref('')
const generating = ref(false)
const generatedEmail = ref<any>(null)
const announcements = ref<any[]>([])

// 可用域名列表
const availableDomains = ref([
  'ofun.my',
  'ofun.io',
  'temp.email',
  'disposable.email'
])

// 用户操作处理
const handleUserAction = (command: string) => {
  if (command === 'profile') {
    router.push('/profile')
  } else if (command === 'logout') {
    userStore.logout()
    ElMessage.success('已退出登录')
  }
}

// 生成邮箱
const generateEmail = async () => {
  if (!selectedDomain.value) {
    ElMessage.warning('请选择域名')
    return
  }

  generating.value = true
  try {
    const result = await userStore.generateEmail({
      domain: selectedDomain.value,
      customName: customName.value || undefined
    })

    if (result.success) {
      generatedEmail.value = result.email
      customName.value = ''
      ElMessage.success('临时邮箱生成成功！')
    } else {
      ElMessage.error(result.message || '生成失败')
    }
  } catch (error) {
    console.error('Generate email error:', error)
    ElMessage.error('生成失败，请稍后重试')
  } finally {
    generating.value = false
  }
}

// 复制邮箱地址
const copyEmail = () => {
  if (generatedEmail.value) {
    copy(generatedEmail.value.fullEmail)
    ElMessage.success('邮箱地址已复制到剪贴板')
  }
}

// 复制授权密钥
const copySecret = () => {
  if (generatedEmail.value) {
    copy(generatedEmail.value.secret)
    ElMessage.success('授权密钥已复制到剪贴板')
  }
}

// 再生成一个
const generateAnother = () => {
  generatedEmail.value = null
}

// 获取系统公告
const fetchAnnouncements = async () => {
  try {
    const response = await fetch('https://ofun-email-system.htmljs.workers.dev/api/announcements')
    const result = await response.json()
    if (result.success) {
      announcements.value = result.announcements || []
    }
  } catch (error) {
    console.error('Failed to fetch announcements:', error)
  }
}

// 组件挂载时
onMounted(async () => {
  // 设置默认域名
  if (availableDomains.value.length > 0) {
    selectedDomain.value = availableDomains.value[0]
  }

  // 获取系统公告
  await fetchAnnouncements()

  // 如果用户已登录，获取用户信息
  if (userStore.isLoggedIn) {
    await userStore.fetchProfile()
  }
})
</script>
