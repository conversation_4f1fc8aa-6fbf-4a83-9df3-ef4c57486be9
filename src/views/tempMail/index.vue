<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <!-- 导航栏 -->
    <nav class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
      <div class="container mx-auto px-4 py-4 flex justify-between items-center">
        <router-link to="/" class="flex items-center space-x-2">
          <i class="fas fa-envelope text-blue-600 text-2xl"></i>
          <h1 class="text-xl font-bold text-gray-800">TempMail</h1>
        </router-link>

        <div class="flex items-center space-x-4">
          <router-link to="/" class="text-gray-600 hover:text-blue-600 transition-colors">
            首页
          </router-link>
          <router-link to="/pricing" class="text-gray-600 hover:text-blue-600 transition-colors">
            配额购买
          </router-link>
          <router-link to="/profile" class="text-gray-600 hover:text-blue-600 transition-colors">
            个人中心
          </router-link>
        </div>
      </div>
    </nav>

    <div class="container mx-auto px-4 py-6 max-w-7xl">
      <!-- 第一行：授权密钥查询 + 邮箱信息 -->
      <div class="mb-6 grid grid-cols-1 gap-6" :class="store.currentEmailData ? 'lg:grid-cols-2' : ''">
        <!-- 授权密钥查询卡片 -->
        <SecretKeyQuery />
        
        <!-- 邮箱信息卡片 -->
        <EmailInfo v-if="store.currentEmailData" />
      </div>

      <!-- 邮件列表区域 -->
      <div class="flex-1">
        <div class="bento-card h-full">
          <!-- 邮件操作按钮 -->
          <MailActions v-if="store.currentEmailData" />

          <!-- 邮件列表标题 -->
          <MailListHeader />

          <!-- 空状态 -->
          <EmptyState v-if="store.mails.length === 0" />

          <!-- 邮件列表 -->
          <MailList v-else />
        </div>
      </div>
    </div>

    <!-- 邮件详情弹窗 -->
    <MailDetailModal />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useTempMailStore } from '../../stores/tempMail'
import SecretKeyQuery from './components/SecretKeyQuery.vue'
import EmailInfo from './components/EmailInfo.vue'
import MailActions from './components/MailActions.vue'
import MailListHeader from './components/MailListHeader.vue'
import EmptyState from './components/EmptyState.vue'
import MailList from './components/MailList.vue'
import MailDetailModal from './components/MailDetailModal.vue'

const route = useRoute()
const store = useTempMailStore()

onMounted(async () => {
  store.initializeFromStorage()

  // 检查 URL 参数中是否有 secret，如果有则自动查询
  const secret = route.query.secret as string
  if (secret && !store.currentEmailData) {
    try {
      await store.queryEmailBySecret(secret)
    } catch (error) {
      console.error('Auto query failed:', error)
    }
  }
})

onUnmounted(() => {
  store.stopAutoRefresh()
})
</script>
