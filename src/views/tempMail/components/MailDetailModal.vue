<template>
  <div
    v-if="store.selectedMail"
    class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 lg:p-6 z-50"
    @click="store.selectedMail = null"
  >
    <div
      class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] flex flex-col"
      @click.stop
    >
      <!-- 固定的标题栏 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-white rounded-t-2xl">
        <h2 class="text-2xl font-bold text-gray-800 flex items-center">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
            <i class="fas fa-envelope-open text-white text-sm"></i>
          </div>
          邮件详情
        </h2>
        <button
          @click="store.selectedMail = null"
          class="w-10 h-10 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-all duration-200 flex items-center justify-center"
        >
          <i class="fas fa-times text-lg"></i>
        </button>
      </div>

      <!-- 可滚动的内容区域 -->
      <div class="flex-1 overflow-y-auto p-6">

        <div class="space-y-6">
          <!-- 邮件基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 发件人 -->
            <div class="bg-blue-50 rounded-xl p-4 border border-blue-200">
              <div class="flex items-center gap-3 mb-2">
                <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                  <i class="fas fa-user text-white text-sm"></i>
                </div>
                <label class="text-sm font-semibold text-blue-700">发件人</label>
              </div>
              <div class="text-gray-800 font-medium break-all">
                {{ store.selectedMail.from }}
              </div>
            </div>

            <!-- 收件人 -->
            <div class="bg-green-50 rounded-xl p-4 border border-green-200">
              <div class="flex items-center gap-3 mb-2">
                <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                  <i class="fas fa-envelope text-white text-sm"></i>
                </div>
                <label class="text-sm font-semibold text-green-700">收件人</label>
              </div>
              <div class="text-gray-800 font-medium break-all">
                {{ store.selectedMail.to }}
              </div>
            </div>

            <!-- 主题 -->
            <div class="bg-purple-50 rounded-xl p-4 border border-purple-200">
              <div class="flex items-center gap-3 mb-2">
                <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                  <i class="fas fa-tag text-white text-sm"></i>
                </div>
                <label class="text-sm font-semibold text-purple-700">主题</label>
              </div>
              <div class="text-gray-800 font-medium">
                {{ store.selectedMail.subject || "(无主题)" }}
              </div>
            </div>

            <!-- 时间 -->
            <div class="bg-orange-50 rounded-xl p-4 border border-orange-200">
              <div class="flex items-center gap-3 mb-2">
                <div class="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                  <i class="fas fa-clock text-white text-sm"></i>
                </div>
                <label class="text-sm font-semibold text-orange-700">时间</label>
              </div>
              <div class="text-gray-800 font-medium">
                {{ formatDate(store.selectedMail.date) }}
              </div>
            </div>
          </div>

          <!-- 验证码区域 -->
          <div
            v-if="extractedCode"
            class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-6"
          >
            <div class="flex items-center gap-3 mb-4">
              <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                <i class="fas fa-key text-white text-sm"></i>
              </div>
              <label class="text-sm font-semibold text-yellow-700">检测到验证码</label>
            </div>
            <div class="flex items-center gap-4">
              <div class="text-3xl font-mono font-bold text-yellow-800 bg-white px-6 py-3 rounded-lg border border-yellow-300 select-all">
                {{ extractedCode }}
              </div>
              <button
                @click="copyCode"
                class="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
                title="复制验证码"
              >
                <i class="fas fa-copy"></i>
                复制
              </button>
            </div>
          </div>

          <!-- 邮件内容 -->
          <div>
            <div class="flex items-center gap-3 mb-4">
              <div class="w-8 h-8 bg-gray-500 rounded-lg flex items-center justify-center">
                <i class="fas fa-file-text text-white text-sm"></i>
              </div>
              <label class="text-sm font-semibold text-gray-700">邮件内容</label>
            </div>

            <!-- 邮件内容切换按钮 -->
            <div v-if="store.selectedMail.html" class="mb-4 flex gap-2">
              <button
                @click="mailViewMode = 'text'"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2',
                  mailViewMode === 'text'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <i class="fas fa-align-left"></i>
                文本模式
              </button>
              <button
                @click="mailViewMode = 'html'"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2',
                  mailViewMode === 'html'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <i class="fas fa-code"></i>
                HTML源码
              </button>
              <button
                @click="mailViewMode = 'rendered'"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2',
                  mailViewMode === 'rendered'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <i class="fas fa-eye"></i>
                渲染模式
              </button>
            </div>

            <!-- 文本内容 -->
            <div
              v-if="mailViewMode === 'text' || !store.selectedMail.html"
              class="bg-gray-50 border border-gray-200 rounded-xl p-4 min-h-48 whitespace-pre-wrap leading-relaxed text-gray-800"
            >
              {{ store.selectedMail.text }}
            </div>

            <!-- HTML源码 -->
            <div
              v-else-if="mailViewMode === 'html'"
              class="bg-gray-50 border border-gray-200 rounded-xl p-4 min-h-48 font-mono text-sm whitespace-pre-wrap leading-relaxed text-gray-800"
            >
              {{ store.selectedMail.html }}
            </div>

            <!-- 渲染的HTML内容 -->
            <div
              v-else-if="mailViewMode === 'rendered'"
              class="bg-white border border-gray-200 rounded-xl p-4 min-h-48 leading-relaxed email-content"
              v-html="sanitizedHtmlContent"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useClipboard } from '@vueuse/core'
import { useTempMailStore } from '../../../stores/tempMail'
import { formatDateToChinaTime } from '../../../utils/dateFormat'

const store = useTempMailStore()
const mailViewMode = ref<'text' | 'html' | 'rendered'>('rendered')

const extractedCode = computed(() => {
  if (!store.selectedMail) return null
  return store.emailService.extractVerificationCode(store.selectedMail.text)
})

const { copy, isSupported } = useClipboard()

const sanitizedHtmlContent = computed(() => {
  if (!store.selectedMail?.html) return ''

  // 基本的HTML清理，移除潜在的恶意脚本
  return store.selectedMail.html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
})

const formatDate = (dateStr: string) => {
  return formatDateToChinaTime(dateStr)
}

const copyCode = async () => {
  if (extractedCode.value) {
    try {
      if (isSupported.value) {
        await copy(extractedCode.value)
        ElMessage.success('验证码已复制到剪贴板')
      } else {
        // 降级处理：创建临时输入框
        const textArea = document.createElement('textarea')
        textArea.value = extractedCode.value
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        ElMessage.success('验证码已复制到剪贴板')
      }
    } catch (error) {
      ElMessage.error('复制失败，请手动复制')
    }
  }
}
</script>

<style scoped>
/* 邮件内容样式 */
.email-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.email-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.email-content :deep(table) {
  max-width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.email-content :deep(td),
.email-content :deep(th) {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
}

.email-content :deep(a) {
  color: #3b82f6;
  text-decoration: underline;
}

.email-content :deep(a:hover) {
  color: #1d4ed8;
}

.email-content :deep(blockquote) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #6b7280;
  font-style: italic;
}

.email-content :deep(pre) {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
}

.email-content :deep(code) {
  background-color: #f3f4f6;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.875em;
}
</style>
