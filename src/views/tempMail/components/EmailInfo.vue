<template>
  <div 
    class="bento-card"
    :class="{
      'border-red-300 bg-red-50/30': store.isExpired,
      'border-green-300 bg-green-50/30': !store.isExpired,
    }"
  >
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
      <div class="flex-1">
        <h2 class="text-xl lg:text-2xl font-bold bento-text-primary mb-4 flex items-center flex-wrap gap-2">
          <i class="fas fa-envelope bento-accent text-2xl lg:text-3xl mr-2"></i>
          当前邮箱信息
          <span
            v-if="store.isExpired"
            class="bento-tag bg-red-100 text-red-700 border-red-200"
          >已过期</span>
          <span
            v-else
            class="bento-tag bg-green-100 text-green-700 border-green-200"
          >正常</span>
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div class="space-y-2">
            <label class="block bento-text-secondary text-sm font-semibold">邮箱地址</label>
            <div class="bento-input bg-blue-50 border-blue-200 text-blue-700 font-mono font-semibold text-sm lg:text-base">
              {{ store.currentEmailAddress }}
            </div>
          </div>

          <div class="space-y-2">
            <label class="block bento-text-secondary text-sm font-semibold">过期时间</label>
            <div
              class="bento-input font-medium text-sm lg:text-base"
              :class="{
                'bg-red-50 border-red-200 text-red-700': store.isExpired,
                'bg-green-50 border-green-200 text-green-700': !store.isExpired,
              }"
            >
              {{ formatDate(store.currentEmailData?.expire || '') }}
              <span v-if="store.isExpired" class="ml-2 font-bold">(已过期)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useTempMailStore } from '../../../stores/tempMail'
import { formatExpireTime } from '../../../utils/dateFormat'

const store = useTempMailStore()

const formatDate = (dateStr: string) => {
  return formatExpireTime(dateStr)
}
</script>
