<template>
  <div class="bento-card">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
      <div class="flex-1">
        <h2 class="text-xl lg:text-2xl font-bold bento-text-primary mb-4 flex items-center">
          <i class="fas fa-key bento-accent text-2xl lg:text-3xl mr-3"></i>
          授权密钥查询
        </h2>
        <div class="flex flex-col sm:flex-row gap-3">
          <input
            v-model="secretKey"
            type="text"
            placeholder="请输入授权密钥"
            class="bento-input bg-gray-100 flex-1"
            @keyup.enter="handleQuery"
          />
          <div class="flex gap-2">
            <button
              @click="handleQuery"
              :disabled="!secretKey || store.queryLoading"
              class="bento-button disabled:opacity-50 disabled:cursor-not-allowed flex items-center whitespace-nowrap"
            >
              <i
                class="fas fa-search mr-2"
                :class="{ 'animate-spin': store.queryLoading }"
              ></i>
              {{ store.queryLoading ? "查询中..." : "查询邮箱" }}
            </button>
            <button
              v-if="store.currentEmailData"
              @click="handleClear"
              class="bento-button bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 flex items-center whitespace-nowrap"
            >
              <i class="fas fa-times mr-2"></i>
              清除当前邮箱
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useTempMailStore } from '../../../stores/tempMail'

const store = useTempMailStore()
const secretKey = ref('')

const handleQuery = async () => {
  try {
    const result = await store.queryEmailBySecret(secretKey.value)
    if (result.success) {
      ElMessage.success(result.message)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('查询失败，请检查网络连接')
  }
}

const handleClear = () => {
  store.clearCurrentEmail()
  secretKey.value = ''
  ElMessage.info('已清除当前邮箱')
}

onMounted(() => {
  // 密钥现在由 Pinia 持久化自动管理，不需要手动恢复
  // 如果有当前邮箱数据，可以显示相关信息
  if (store.currentEmailData) {
    console.log('Current email data restored from persistence')
  }
})
</script>
