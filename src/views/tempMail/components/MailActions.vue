<template>
  <div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div class="flex flex-wrap gap-3">
        <button
          @click="handleRefresh"
          :disabled="store.loading || store.isExpired"
          class="bento-button disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <i
            class="fas fa-sync-alt mr-2"
            :class="{ 'animate-spin': store.loading }"
          ></i>
          {{ store.loading ? "接收中..." : "接收邮件" }}
        </button>

        <button
          @click="handleClearMails"
          :disabled="store.mails.length === 0"
          class="bento-button bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <i class="fas fa-trash mr-2"></i>
          清空邮件
        </button>

        <button
          @click="handleToggleAutoRefresh"
          :disabled="!store.currentEmailData || store.isExpired"
          :class="
            store.autoRefreshEnabled
              ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'
              : 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
          "
          class="bento-button disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <i
            :class="
              store.autoRefreshEnabled
                ? 'fas fa-pause mr-2'
                : 'fas fa-play mr-2'
            "
          ></i>
          {{ store.autoRefreshEnabled ? "停止自动刷新" : "启用自动刷新" }}
        </button>
      </div>

      <div v-if="store.lastUpdateTime" class="text-sm text-gray-500">
        最后更新：{{ store.lastUpdateTime }}
      </div>
    </div>

    <!-- 自动刷新状态指示器 -->
    <div
      v-if="store.autoRefreshEnabled"
      class="mt-3 flex items-center justify-center"
    >
      <div class="bento-tag bg-green-100 text-green-700 border-green-200">
        <i class="fas fa-circle animate-pulse mr-1"></i>
        自动刷新中 ({{ store.autoRefreshInterval }}s)
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useTempMailStore } from '../../../stores/tempMail'

const store = useTempMailStore()

// 监听新邮件通知
watch(() => store.newMailsNotification, (notification) => {
  if (notification) {
    ElMessage.success(`收到 ${notification.count} 封新邮件，自动刷新已停止`)
    // 清除通知
    store.newMailsNotification = null
  }
})

const handleRefresh = async () => {
  try {
    const count = await store.refreshMails()
    ElMessage.success(`成功接收 ${count} 封邮件`)
  } catch (error: any) {
    ElMessage.error(error.message || '接收邮件失败，请检查网络连接')
  }
}

const handleClearMails = () => {
  store.clearMails()
  ElMessage.info('邮件列表已清空')
}

const handleToggleAutoRefresh = () => {
  try {
    const result = store.toggleAutoRefresh()
    if (result.enabled) {
      ElMessage.success(`已启用自动刷新，每 ${result.interval} 秒检查一次`)
    } else {
      ElMessage.info('已停止自动刷新')
    }
  } catch (error: any) {
    ElMessage.error(error.message)
  }
}
</script>
