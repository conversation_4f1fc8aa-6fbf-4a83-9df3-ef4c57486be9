<template>
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
    <h2 class="text-xl lg:text-2xl font-bold bento-text-primary flex items-center flex-wrap gap-2">
      <i class="fas fa-inbox bento-accent text-2xl lg:text-3xl mr-2"></i>
      邮件列表
      <span class="bento-tag">{{ store.mails.length }} 封邮件</span>
    </h2>
    <div
      v-if="store.currentEmailData"
      class="flex items-center gap-2 flex-wrap"
    >
      <span class="bento-tag text-xs lg:text-sm">
        {{ store.currentEmailData.mail }}@{{ store.currentEmailData.domain }}
      </span>
      <span
        class="bento-tag text-xs lg:text-sm"
        :class="{
          'bg-red-100 text-red-700 border-red-200': store.isExpired,
          'bg-green-100 text-green-700 border-green-200': !store.isExpired,
        }"
      >
        {{ store.isExpired ? "已过期" : "正常" }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useTempMailStore } from '../../../stores/tempMail'

const store = useTempMailStore()
</script>
