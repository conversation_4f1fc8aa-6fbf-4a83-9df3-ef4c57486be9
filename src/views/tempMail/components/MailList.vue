<template>
  <div class="space-y-4">
    <!-- 邮件操作栏 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-white/50 rounded-2xl backdrop-blur-sm border border-white/30">
      <div class="flex items-center gap-3 flex-wrap">
        <label class="flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            :checked="store.selectedMails.length === store.mails.length && store.mails.length > 0"
            @change="(e) => store.handleSelectAll((e.target as HTMLInputElement).checked)"
            class="rounded border-2 border-white/30 bg-white/50"
          />
          <span class="bento-text-secondary text-sm font-medium">全选</span>
        </label>
        <button
          v-if="store.selectedMails.length > 0"
          @click="handleBatchDelete"
          :disabled="store.batchDeleteLoading"
          class="bento-button bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-sm py-2 px-3 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <i
            class="fas fa-trash-alt mr-1"
            :class="{ 'animate-spin': store.batchDeleteLoading }"
          ></i>
          删除 ({{ store.selectedMails.length }})
        </button>
        <button
          @click="handleClearMails"
          :disabled="store.mails.length === 0"
          class="bento-button bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-sm py-2 px-3 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <i class="fas fa-broom mr-1"></i>
          清空
        </button>
      </div>
      <div v-if="store.lastUpdateTime" class="text-xs lg:text-sm bento-text-muted">
        最后更新：{{ store.lastUpdateTime }}
      </div>
    </div>

    <!-- 邮件列表 -->
    <div class="space-y-2 max-h-[60vh] overflow-y-auto">
      <div
        v-for="mail in store.mails"
        :key="mail.id"
        class="group relative bg-white/70 hover:bg-white/90 border border-white/50 hover:border-blue-200 rounded-xl p-5 cursor-pointer transition-all duration-200 hover:shadow-xl hover:shadow-blue-100/50 backdrop-blur-sm"
        @click="handleMailClick(mail)"
      >
        <div class="flex items-center gap-4">
          <!-- 选择框 -->
          <div
            class="flex-shrink-0 p-1 hover:bg-white/50 rounded-lg transition-colors"
            @click.stop
          >
            <input
              type="checkbox"
              :checked="store.selectedMails.some((m) => m.id === mail.id)"
              @change="(e) => store.handleMailSelection(mail, (e.target as HTMLInputElement).checked)"
              class="rounded border-2 border-white/30 bg-white/50 w-4 h-4"
            />
          </div>

          <!-- 邮件图标 -->
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <i class="fas fa-envelope text-white text-sm"></i>
            </div>
          </div>

          <!-- 邮件信息 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-3 mb-3">
              <h3 class="font-semibold text-gray-800 truncate text-lg">
                {{ mail.subject || "(无主题)" }}
              </h3>
              <!-- 最新标签 -->
              <span
                v-if="store.mails.indexOf(mail) === 0"
                class="flex-shrink-0 text-xs text-white bg-gradient-to-r from-red-500 to-pink-500 px-3 py-1 rounded-full font-medium shadow-sm"
              >
                <i class="fas fa-star mr-1"></i>最新
              </span>
              <span class="flex-shrink-0 text-xs text-gray-600 bg-gray-100/80 px-3 py-1 rounded-full font-medium">
                {{ formatRelativeTime(mail.date) }}
              </span>
            </div>
            <!-- 验证码显示区域 -->
            <div
              v-if="extractVerificationCode(mail.text)"
              class="mb-2 p-3 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-key text-white text-xs"></i>
                  </div>
                  <span class="text-sm text-yellow-800 font-medium">
                    识别到验证码
                  </span>
                </div>
                <div class="flex items-center gap-2">
                  <button
                    @click.stop="copyVerificationCode(extractVerificationCode(mail.text))"
                    class="group relative flex items-center gap-2 text-lg font-mono font-bold text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 px-4 py-2 rounded-lg transition-all duration-200 hover:shadow-lg hover:scale-105 cursor-pointer"
                    title="点击复制验证码"
                  >
                    <span class="select-all">{{ extractVerificationCode(mail.text) }}</span>
                    <i class="fas fa-copy text-sm opacity-70 group-hover:opacity-100 transition-opacity"></i>
                    <!-- 提示文字 -->
                    <span class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      点击复制
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <div class="flex items-center gap-2 text-sm text-gray-600">
              <div class="flex items-center gap-2 bg-gray-50 px-3 py-1 rounded-lg">
                <i class="fas fa-clock text-orange-500 text-xs"></i>
                <span class="text-xs font-medium">{{ formatDate(mail.date) }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center gap-3 opacity-0 group-hover:opacity-100 transition-all duration-200">
            <button
              @click.stop="store.selectMail(mail)"
              title="查看详情"
              class="w-10 h-10 rounded-xl bg-blue-100 text-blue-600 hover:bg-blue-200 hover:scale-110 transition-all duration-200 flex items-center justify-center shadow-sm"
            >
              <i class="fas fa-eye text-sm"></i>
            </button>
            <button
              @click.stop="handleDeleteMail(mail.id)"
              title="删除邮件"
              class="w-10 h-10 rounded-xl bg-red-100 text-red-600 hover:bg-red-200 hover:scale-110 transition-all duration-200 flex items-center justify-center shadow-sm"
            >
              <i class="fas fa-trash text-sm"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { useClipboard } from '@vueuse/core'
import { useTempMailStore } from '../../../stores/tempMail'
import type { MailItem } from '../../../services/tempmail'
import { formatDateToChinaTime, formatRelativeTime } from '../../../utils/dateFormat'

const store = useTempMailStore()
const { copy, isSupported } = useClipboard()

const formatDate = (dateStr: string) => {
  return formatDateToChinaTime(dateStr, 'YYYY-MM-DD HH:mm')
}

const extractVerificationCode = (text: string): string | null => {
  if (!text) return null
  return store.emailService.extractVerificationCode(text)
}

const copyVerificationCode = async (code: string | null) => {
  if (!code) return

  try {
    if (isSupported.value) {
      await copy(code)
      ElMessage.success(`验证码 ${code} 已复制到剪贴板`)
    } else {
      // 降级处理：创建临时输入框
      const textArea = document.createElement('textarea')
      textArea.value = code
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success(`验证码 ${code} 已复制到剪贴板`)
    }
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const handleMailClick = (mail: MailItem) => {
  store.selectMail(mail)
}

const handleDeleteMail = (mailId: string) => {
  store.deleteMail(mailId)
  ElMessage.success('邮件删除成功')
}

const handleClearMails = () => {
  store.clearMails()
  ElMessage.info('邮件列表已清空')
}

const handleBatchDelete = async () => {
  if (store.selectedMails.length === 0) {
    ElMessage.warning('请选择要删除的邮件')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${store.selectedMails.length} 封邮件吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const result = await store.batchDeleteMails()
    if (result.success) {
      ElMessage.success(`成功删除 ${result.deletedCount} 封邮件`)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败，请检查网络连接')
    }
  }
}
</script>
