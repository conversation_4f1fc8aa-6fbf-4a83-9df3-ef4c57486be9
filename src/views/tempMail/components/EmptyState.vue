<template>
  <div class="text-center py-12 lg:py-16">
    <i class="fas fa-envelope-open text-6xl lg:text-8xl bento-text-muted mb-4 lg:mb-6"></i>
    <p class="bento-text-secondary text-base lg:text-lg mb-4 lg:mb-6">
      {{ getEmptyDescription() }}
    </p>
    <button
      v-if="store.currentEmailData && !store.isExpired"
      @click="handleRefresh"
      :disabled="store.loading"
      class="bento-button disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto"
    >
      <i
        class="fas fa-download mr-2"
        :class="{ 'animate-spin': store.loading }"
      ></i>
      {{ store.loading ? "接收中..." : "接收邮件" }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { useTempMailStore } from '../../../stores/tempMail'

const store = useTempMailStore()

const getEmptyDescription = () => {
  if (!store.currentEmailData) {
    return "请先输入授权密钥查询邮箱"
  } else if (store.isExpired) {
    return "邮箱已过期，无法接收邮件"
  } else {
    return "暂无邮件，点击下方按钮接收邮件"
  }
}

const handleRefresh = async () => {
  try {
    const count = await store.refreshMails()
    ElMessage.success(`成功接收 ${count} 封邮件`)
  } catch (error: any) {
    ElMessage.error(error.message || '接收邮件失败，请检查网络连接')
  }
}
</script>
