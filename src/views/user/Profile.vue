<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <!-- 导航栏 -->
    <nav class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
      <div class="container mx-auto px-4 py-4 flex justify-between items-center">
        <router-link to="/" class="flex items-center space-x-2">
          <i class="fas fa-envelope text-blue-600 text-2xl"></i>
          <h1 class="text-xl font-bold text-gray-800">TempMail</h1>
        </router-link>
        
        <div class="flex items-center space-x-4">
          <router-link to="/" class="text-gray-600 hover:text-blue-600 transition-colors">
            首页
          </router-link>
          <router-link to="/pricing" class="text-gray-600 hover:text-blue-600 transition-colors">
            配额购买
          </router-link>
          <el-button @click="handleLogout" type="danger" plain size="small">
            <i class="fas fa-sign-out-alt mr-1"></i>
            退出登录
          </el-button>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mx-auto px-4 py-8 max-w-6xl">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 左侧：用户信息 -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="text-center mb-6">
              <el-avatar :size="80" :src="userStore.user?.avatarUrl" class="mb-4">
                <i class="fas fa-user text-2xl"></i>
              </el-avatar>
              <h2 class="text-xl font-semibold text-gray-800">
                {{ userStore.user?.displayName || userStore.user?.username || '用户' }}
              </h2>
              <p class="text-gray-600 text-sm">{{ userStore.user?.email }}</p>
              <div class="mt-2 flex items-center justify-center space-x-2">
                <el-tag v-if="userStore.user?.emailVerified" type="success" size="small">
                  <i class="fas fa-check-circle mr-1"></i>
                  已验证
                </el-tag>
                <el-tag v-else type="warning" size="small">
                  <i class="fas fa-exclamation-circle mr-1"></i>
                  未验证
                </el-tag>
              </div>
            </div>

            <!-- 配额信息 -->
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white mb-6">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm opacity-90">当前配额</span>
                <i class="fas fa-coins text-yellow-300"></i>
              </div>
              <div class="text-2xl font-bold">{{ userStore.credits?.balance || 0 }}</div>
              <div class="text-xs opacity-75 mt-1">
                总获得: {{ userStore.credits?.totalEarned || 0 }} | 
                总消费: {{ userStore.credits?.totalSpent || 0 }}
              </div>
            </div>

            <!-- 快捷操作 -->
            <div class="space-y-3">
              <router-link to="/" class="block">
                <el-button type="primary" class="w-full">
                  <i class="fas fa-plus mr-2"></i>
                  生成临时邮箱
                </el-button>
              </router-link>
              <router-link to="/pricing" class="block">
                <el-button type="success" plain class="w-full">
                  <i class="fas fa-shopping-cart mr-2"></i>
                  购买配额
                </el-button>
              </router-link>
            </div>
          </div>
        </div>

        <!-- 右侧：功能区域 -->
        <div class="lg:col-span-2 space-y-8">
          <!-- 兑换码 -->
          <div class="bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
              <i class="fas fa-ticket-alt text-blue-600 mr-2"></i>
              兑换配额码
            </h3>
            <div class="flex space-x-4">
              <el-input
                v-model="redeemCode"
                placeholder="请输入兑换码"
                class="flex-1"
                :maxlength="20"
              />
              <el-button
                type="primary"
                :loading="redeemLoading"
                :disabled="!redeemCode.trim()"
                @click="handleRedeemCode"
              >
                兑换
              </el-button>
            </div>
            <p class="text-sm text-gray-500 mt-2">
              输入有效的兑换码可以获得配额奖励
            </p>
          </div>

          <!-- 交易记录 -->
          <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-history text-blue-600 mr-2"></i>
                交易记录
              </h3>
              <el-button size="small" @click="refreshTransactions">
                <i class="fas fa-refresh mr-1"></i>
                刷新
              </el-button>
            </div>

            <div v-if="transactionsLoading" class="text-center py-8">
              <el-icon class="is-loading text-2xl text-gray-400">
                <Loading />
              </el-icon>
              <p class="text-gray-500 mt-2">加载中...</p>
            </div>

            <div v-else-if="transactions.length === 0" class="text-center py-8">
              <i class="fas fa-inbox text-4xl text-gray-300 mb-4"></i>
              <p class="text-gray-500">暂无交易记录</p>
            </div>

            <div v-else class="space-y-3">
              <div
                v-for="transaction in transactions"
                :key="transaction.id"
                class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <div :class="[
                    'w-10 h-10 rounded-full flex items-center justify-center',
                    transaction.type === 'earn' ? 'bg-green-100 text-green-600' :
                    transaction.type === 'spend' ? 'bg-red-100 text-red-600' :
                    transaction.type === 'refund' ? 'bg-blue-100 text-blue-600' :
                    'bg-gray-100 text-gray-600'
                  ]">
                    <i :class="[
                      transaction.type === 'earn' ? 'fas fa-plus' :
                      transaction.type === 'spend' ? 'fas fa-minus' :
                      transaction.type === 'refund' ? 'fas fa-undo' :
                      'fas fa-exchange-alt'
                    ]"></i>
                  </div>
                  <div>
                    <p class="font-medium text-gray-800">{{ transaction.description }}</p>
                    <p class="text-sm text-gray-500">
                      {{ formatDate(transaction.createdAt) }}
                    </p>
                  </div>
                </div>
                <div class="text-right">
                  <p :class="[
                    'font-semibold',
                    transaction.type === 'earn' ? 'text-green-600' :
                    transaction.type === 'spend' ? 'text-red-600' :
                    'text-gray-600'
                  ]">
                    {{ transaction.type === 'earn' ? '+' : '' }}{{ transaction.amount }}
                  </p>
                  <p class="text-sm text-gray-500">
                    余额: {{ transaction.balanceAfter }}
                  </p>
                </div>
              </div>

              <!-- 分页 -->
              <div v-if="pagination.totalPages > 1" class="flex justify-center mt-6">
                <el-pagination
                  v-model:current-page="pagination.page"
                  :page-size="pagination.limit"
                  :total="pagination.total"
                  layout="prev, pager, next"
                  @current-change="handlePageChange"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useUserStore } from '../../stores/user'
import { formatDateToChinaTime } from '../../utils/dateFormat'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const redeemCode = ref('')
const redeemLoading = ref(false)
const transactions = ref<any[]>([])
const transactionsLoading = ref(false)
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 0
})

// 格式化日期
const formatDate = (dateStr: string) => {
  return formatDateToChinaTime(dateStr, 'YYYY-MM-DD HH:mm')
}

// 处理兑换码
const handleRedeemCode = async () => {
  if (!redeemCode.value.trim()) {
    ElMessage.warning('请输入兑换码')
    return
  }

  redeemLoading.value = true
  try {
    const result = await userStore.redeemCode(redeemCode.value.trim())
    
    if (result.success) {
      ElMessage.success(`成功兑换 ${result.creditsAwarded} 配额！`)
      redeemCode.value = ''
      // 刷新交易记录
      await refreshTransactions()
    } else {
      ElMessage.error(result.message || '兑换失败')
    }
  } catch (error) {
    console.error('Redeem error:', error)
    ElMessage.error('兑换失败，请稍后重试')
  } finally {
    redeemLoading.value = false
  }
}

// 获取交易记录
const fetchTransactions = async (page = 1) => {
  transactionsLoading.value = true
  try {
    const result = await userStore.fetchTransactions(page, pagination.value.limit)
    
    if (result.success) {
      transactions.value = result.transactions || []
      pagination.value = {
        ...pagination.value,
        ...result.pagination
      }
    } else {
      ElMessage.error(result.message || '获取交易记录失败')
    }
  } catch (error) {
    console.error('Fetch transactions error:', error)
    ElMessage.error('获取交易记录失败')
  } finally {
    transactionsLoading.value = false
  }
}

// 刷新交易记录
const refreshTransactions = () => {
  fetchTransactions(pagination.value.page)
}

// 处理分页变化
const handlePageChange = (page: number) => {
  fetchTransactions(page)
}

// 处理登出
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await userStore.logout()
    ElMessage.success('已退出登录')
    router.push('/')
  } catch (error) {
    // 用户取消操作
  }
}

// 组件挂载时
onMounted(async () => {
  // 获取用户资料
  await userStore.fetchProfile()
  
  // 获取交易记录
  await fetchTransactions()
})
</script>

<style scoped>
.el-avatar {
  border: 3px solid #e5e7eb;
}

.el-pagination {
  justify-content: center;
}
</style>
