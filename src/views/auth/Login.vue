<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center py-12 px-4">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div class="text-center">
        <router-link to="/" class="inline-flex items-center space-x-2 text-2xl font-bold text-gray-800 mb-8">
          <i class="fas fa-envelope text-blue-600"></i>
          <span>TempMail</span>
        </router-link>
        <h2 class="text-3xl font-bold text-gray-900 mb-2">
          欢迎回来
        </h2>
        <p class="text-gray-600">
          登录您的账户以继续使用服务
        </p>
      </div>

      <!-- 登录表单 -->
      <div class="bg-white rounded-xl shadow-lg p-8">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          @submit.prevent="handleLogin"
          label-position="top"
          class="space-y-6"
        >
          <el-form-item label="邮箱地址" prop="email">
            <el-input
              v-model="loginForm.email"
              type="email"
              placeholder="请输入您的邮箱地址"
              size="large"
              :prefix-icon="Message"
            />
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入您的密码"
              size="large"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="userStore.loading"
              @click="handleLogin"
              class="w-full"
            >
              <i class="fas fa-sign-in-alt mr-2"></i>
              登录
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 分割线 -->
        <div class="relative my-6">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">或</span>
          </div>
        </div>

        <!-- GitHub 登录 -->
        <el-button
          size="large"
          :loading="githubLoading"
          @click="handleGitHubLogin"
          class="w-full"
          style="background-color: #24292e; border-color: #24292e; color: white;"
        >
          <i class="fab fa-github mr-2"></i>
          使用 GitHub 登录
        </el-button>

        <!-- 底部链接 -->
        <div class="mt-6 text-center space-y-2">
          <p class="text-sm text-gray-600">
            还没有账户？
            <router-link to="/register" class="text-blue-600 hover:text-blue-700 font-medium">
              立即注册
            </router-link>
          </p>
          <router-link to="/" class="text-sm text-gray-500 hover:text-gray-700">
            返回首页
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElForm } from 'element-plus'
import { Message, Lock } from '@element-plus/icons-vue'
import { useUserStore } from '../../stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref<InstanceType<typeof ElForm>>()

// 表单数据
const loginForm = reactive({
  email: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// GitHub 登录加载状态
const githubLoading = ref(false)

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    const result = await userStore.login({
      email: loginForm.email,
      password: loginForm.password
    })

    if (result.success) {
      ElMessage.success('登录成功！')
      
      // 重定向到原来要访问的页面或首页
      const redirect = route.query.redirect as string || '/'
      router.push(redirect)
    } else {
      ElMessage.error(result.message || '登录失败')
    }
  } catch (error) {
    console.error('Login error:', error)
    ElMessage.error('登录失败，请稍后重试')
  }
}

// 处理 GitHub 登录
const handleGitHubLogin = async () => {
  githubLoading.value = true
  try {
    // 获取 GitHub OAuth URL
    const response = await fetch('https://ofun-email-system.htmljs.workers.dev/api/auth/github')
    const result = await response.json()

    if (result.success && result.authUrl) {
      // 保存 state 到 localStorage
      localStorage.setItem('github-oauth-state', result.state)
      
      // 重定向到 GitHub
      window.location.href = result.authUrl
    } else {
      ElMessage.error('GitHub 登录初始化失败')
    }
  } catch (error) {
    console.error('GitHub login error:', error)
    ElMessage.error('GitHub 登录失败')
  } finally {
    githubLoading.value = false
  }
}

// 处理 GitHub OAuth 回调
const handleGitHubCallback = async () => {
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')
  const state = urlParams.get('state')
  const storedState = localStorage.getItem('github-oauth-state')

  if (code && state && state === storedState) {
    try {
      const response = await fetch('https://ofun-email-system.htmljs.workers.dev/api/auth/github/callback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code, state })
      })

      const result = await response.json()

      if (result.success && result.token) {
        // 保存令牌和用户信息
        userStore.token = result.token
        userStore.user = result.user
        userStore.refreshToken = result.refreshToken

        ElMessage.success('GitHub 登录成功！')

        // 清除 state
        localStorage.removeItem('github-oauth-state')

        // 重定向
        const redirect = route.query.redirect as string || '/'
        router.push(redirect)
      } else {
        ElMessage.error(result.message || 'GitHub 登录失败')
      }
    } catch (error) {
      console.error('GitHub callback error:', error)
      ElMessage.error('GitHub 登录失败')
    }
  }
}

// 组件挂载时检查是否有 GitHub 回调
onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search)
  if (urlParams.get('code')) {
    handleGitHubCallback()
  }
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 24px;
}

.el-input {
  --el-input-height: 48px;
}

.el-button {
  height: 48px;
  font-size: 16px;
}
</style>
