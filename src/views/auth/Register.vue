<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center py-12 px-4">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div class="text-center">
        <router-link to="/" class="inline-flex items-center space-x-2 text-2xl font-bold text-gray-800 mb-8">
          <i class="fas fa-envelope text-blue-600"></i>
          <span>TempMail</span>
        </router-link>
        <h2 class="text-3xl font-bold text-gray-900 mb-2">
          创建账户
        </h2>
        <p class="text-gray-600">
          注册即可获得5个免费积分
        </p>
      </div>

      <!-- 注册表单 -->
      <div class="bg-white rounded-xl shadow-lg p-8">
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          @submit.prevent="handleRegister"
          label-position="top"
          class="space-y-6"
        >
          <el-form-item label="邮箱地址" prop="email">
            <el-input
              v-model="registerForm.email"
              type="email"
              placeholder="请输入您的邮箱地址"
              size="large"
              :prefix-icon="Message"
            />
          </el-form-item>

          <el-form-item label="用户名（可选）" prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="请输入用户名"
              size="large"
              :prefix-icon="User"
            />
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码（至少6位）"
              size="large"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              size="large"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>

          <!-- 验证码字段 -->
          <el-form-item v-if="verificationSent" label="邮箱验证码" prop="verificationCode">
            <div class="flex space-x-2">
              <el-input
                v-model="registerForm.verificationCode"
                placeholder="请输入6位验证码"
                size="large"
                :prefix-icon="Message"
                maxlength="6"
                class="flex-1"
              />
              <el-button
                :disabled="countdown > 0 || sendingCode"
                :loading="sendingCode"
                @click="sendVerificationCode"
                size="large"
              >
                {{ countdown > 0 ? `${countdown}s` : '重新发送' }}
              </el-button>
            </div>
            <p class="text-sm text-gray-500 mt-1">
              验证码已发送到您的邮箱，请查收
            </p>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="userStore.loading || sendingCode"
              @click="handleRegister"
              class="w-full"
            >
              <i class="fas fa-user-plus mr-2"></i>
              {{ verificationSent ? '完成注册' : '发送验证码' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 分割线 -->
        <div class="relative my-6">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">或</span>
          </div>
        </div>

        <!-- GitHub 注册 -->
        <el-button
          size="large"
          :loading="githubLoading"
          @click="handleGitHubRegister"
          class="w-full"
          style="background-color: #24292e; border-color: #24292e; color: white;"
        >
          <i class="fab fa-github mr-2"></i>
          使用 GitHub 注册
        </el-button>

        <!-- 底部链接 -->
        <div class="mt-6 text-center space-y-2">
          <p class="text-sm text-gray-600">
            已有账户？
            <router-link to="/login" class="text-blue-600 hover:text-blue-700 font-medium">
              立即登录
            </router-link>
          </p>
          <router-link to="/" class="text-sm text-gray-500 hover:text-gray-700">
            返回首页
          </router-link>
        </div>

        <!-- 注册福利提示 -->
        <div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="flex items-center space-x-2 text-green-800">
            <i class="fas fa-gift text-green-600"></i>
            <div class="text-sm">
              <p class="font-medium">新用户福利</p>
              <p>注册即可获得5个免费积分，每个积分可生成一个临时邮箱</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElForm } from 'element-plus'
import { Message, Lock, User } from '@element-plus/icons-vue'
import { useUserStore } from '../../stores/user'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const registerFormRef = ref<InstanceType<typeof ElForm>>()

// 表单数据
const registerForm = reactive({
  email: '',
  username: '',
  password: '',
  confirmPassword: '',
  verificationCode: ''
})

// 验证码相关状态
const verificationSent = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)
const countdownTimer = ref<NodeJS.Timeout | null>(null)

// 自定义验证器
const validateConfirmPassword = (rule: any, value: string, callback: Function) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  username: [
    { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码为6位数字', trigger: 'blur' }
  ]
}

// GitHub 注册加载状态
const githubLoading = ref(false)

// 发送验证码
const sendVerificationCode = async () => {
  if (!registerForm.email) {
    ElMessage.warning('请先输入邮箱地址')
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(registerForm.email)) {
    ElMessage.error('请输入正确的邮箱格式')
    return
  }

  sendingCode.value = true
  try {
    const result = await userStore.register({
      email: registerForm.email,
      username: registerForm.username || undefined,
      password: registerForm.password
    })

    if (result.success && result.requiresVerification) {
      ElMessage.success('验证码已发送到您的邮箱，请查收')
      verificationSent.value = true
      startCountdown()
    } else {
      ElMessage.error(result.message || '发送验证码失败')
    }
  } catch (error) {
    console.error('Send verification code error:', error)
    ElMessage.error('发送验证码失败，请稍后重试')
  } finally {
    sendingCode.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  countdownTimer.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!)
      countdownTimer.value = null
    }
  }, 1000)
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    const valid = await registerFormRef.value.validate()
    if (!valid) return

    if (!verificationSent.value) {
      // 第一次提交，发送验证码
      await sendVerificationCode()
      return
    }

    // 第二次提交，验证验证码并完成注册
    const result = await userStore.register({
      email: registerForm.email,
      username: registerForm.username || undefined,
      password: registerForm.password,
      verificationCode: registerForm.verificationCode
    })

    if (result.success) {
      ElMessage.success('注册成功！请登录您的账户')
      router.push('/login')
    } else {
      ElMessage.error(result.message || '注册失败')
    }
  } catch (error) {
    console.error('Register error:', error)
    ElMessage.error('注册失败，请稍后重试')
  }
}

// 处理 GitHub 注册
const handleGitHubRegister = async () => {
  githubLoading.value = true
  try {
    // 获取 GitHub OAuth URL
    const response = await fetch('https://ofun-email-system.htmljs.workers.dev/api/auth/github')
    const result = await response.json()

    if (result.success && result.authUrl) {
      // 保存 state 到 localStorage
      localStorage.setItem('github-oauth-state', result.state)
      
      // 重定向到 GitHub
      window.location.href = result.authUrl
    } else {
      ElMessage.error('GitHub 注册初始化失败')
    }
  } catch (error) {
    console.error('GitHub register error:', error)
    ElMessage.error('GitHub 注册失败')
  } finally {
    githubLoading.value = false
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 24px;
}

.el-input {
  --el-input-height: 48px;
}

.el-button {
  height: 48px;
  font-size: 16px;
}
</style>
