<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <!-- 导航栏 -->
    <nav class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
      <div class="container mx-auto px-4 py-4 flex justify-between items-center">
        <router-link to="/" class="flex items-center space-x-2">
          <i class="fas fa-envelope text-blue-600 text-2xl"></i>
          <h1 class="text-xl font-bold text-gray-800">TempMail</h1>
        </router-link>
        
        <div class="flex items-center space-x-4">
          <router-link to="/" class="text-gray-600 hover:text-blue-600 transition-colors">
            首页
          </router-link>
          <router-link to="/apikey_mail" class="text-gray-600 hover:text-blue-600 transition-colors">
            密钥查询
          </router-link>
          
          <div v-if="userStore.isLoggedIn" class="flex items-center space-x-3">
            <div class="flex items-center space-x-2 text-sm text-gray-600">
              <i class="fas fa-coins text-yellow-500"></i>
              <span>{{ userStore.credits?.balance || 0 }} 配额</span>
            </div>
            <router-link to="/profile" class="text-gray-600 hover:text-blue-600 transition-colors">
              个人中心
            </router-link>
          </div>
          
          <div v-else class="flex items-center space-x-3">
            <router-link to="/login" class="text-blue-600 hover:text-blue-700 transition-colors">
              登录
            </router-link>
            <router-link to="/register" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              注册
            </router-link>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mx-auto px-4 py-12">
      <!-- 标题区域 -->
      <div class="text-center mb-16">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">
          配额套餐
        </h1>
        <p class="text-lg text-gray-600 mb-8">
          选择适合您的配额套餐，享受更多临时邮箱服务
        </p>

        <!-- 当前配额显示 -->
        <div v-if="userStore.isLoggedIn" class="inline-flex items-center space-x-2 bg-blue-50 border border-blue-200 rounded-lg px-4 py-2">
          <i class="fas fa-coins text-yellow-500"></i>
          <span class="text-blue-800 font-medium">当前配额: {{ userStore.credits?.balance || 0 }}</span>
        </div>
      </div>

      <!-- 配额套餐 -->
      <div class="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
        <!-- 基础套餐 -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200 hover:border-blue-300 transition-colors">
          <div class="text-center">
            <div class="bg-gray-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-star text-gray-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-800 mb-2">基础套餐</h3>
            <div class="text-3xl font-bold text-gray-800 mb-1">¥9.9</div>
            <div class="text-sm text-gray-500 mb-6">50 配额</div>
            
            <ul class="text-sm text-gray-600 space-y-2 mb-6">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                50个临时邮箱
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                30天有效期
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                实时邮件接收
              </li>
            </ul>
            
            <el-button type="primary" class="w-full" @click="handlePurchase('basic')">
              立即购买
            </el-button>
          </div>
        </div>

        <!-- 标准套餐 -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-2 border-blue-300 hover:border-blue-400 transition-colors relative">
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium">推荐</span>
          </div>
          <div class="text-center">
            <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-gem text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-800 mb-2">标准套餐</h3>
            <div class="text-3xl font-bold text-gray-800 mb-1">¥19.9</div>
            <div class="text-sm text-gray-500 mb-6">120 配额</div>
            
            <ul class="text-sm text-gray-600 space-y-2 mb-6">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                120个临时邮箱
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                30天有效期
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                实时邮件接收
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                <span class="text-blue-600 font-medium">赠送20配额</span>
              </li>
            </ul>
            
            <el-button type="primary" class="w-full" @click="handlePurchase('standard')">
              立即购买
            </el-button>
          </div>
        </div>

        <!-- 高级套餐 -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-2 border-purple-300 hover:border-purple-400 transition-colors">
          <div class="text-center">
            <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-crown text-purple-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-800 mb-2">高级套餐</h3>
            <div class="text-3xl font-bold text-gray-800 mb-1">¥39.9</div>
            <div class="text-sm text-gray-500 mb-6">300 配额</div>
            
            <ul class="text-sm text-gray-600 space-y-2 mb-6">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                300个临时邮箱
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                30天有效期
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                实时邮件接收
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                <span class="text-purple-600 font-medium">赠送50配额</span>
              </li>
            </ul>
            
            <el-button type="primary" class="w-full" @click="handlePurchase('premium')">
              立即购买
            </el-button>
          </div>
        </div>

        <!-- 企业套餐 -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-2 border-yellow-300 hover:border-yellow-400 transition-colors">
          <div class="text-center">
            <div class="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-building text-yellow-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-800 mb-2">企业套餐</h3>
            <div class="text-3xl font-bold text-gray-800 mb-1">¥99.9</div>
            <div class="text-sm text-gray-500 mb-6">1000 配额</div>
            
            <ul class="text-sm text-gray-600 space-y-2 mb-6">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                1000个临时邮箱
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                30天有效期
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                实时邮件接收
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                <span class="text-yellow-600 font-medium">赠送200配额</span>
              </li>
            </ul>
            
            <el-button type="primary" class="w-full" @click="handlePurchase('enterprise')">
              立即购买
            </el-button>
          </div>
        </div>
      </div>

      <!-- 功能说明 -->
      <div class="max-w-4xl mx-auto">
        <h2 class="text-2xl font-bold text-gray-800 text-center mb-8">为什么选择我们？</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div class="text-center">
            <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-shield-alt text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold mb-2">隐私保护</h3>
            <p class="text-gray-600 text-sm">完全匿名，保护您的真实邮箱地址不被泄露</p>
          </div>
          
          <div class="text-center">
            <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-bolt text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold mb-2">即时接收</h3>
            <p class="text-gray-600 text-sm">实时接收邮件，支持验证码自动提取功能</p>
          </div>
          
          <div class="text-center">
            <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-clock text-purple-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold mb-2">长期有效</h3>
            <p class="text-gray-600 text-sm">配额永不过期，随时可用，灵活便捷</p>
          </div>
        </div>

        <!-- 常见问题 -->
        <div class="bg-white rounded-xl shadow-lg p-8">
          <h3 class="text-xl font-semibold text-gray-800 mb-6 text-center">常见问题</h3>
          
          <div class="space-y-4">
            <div class="border-b border-gray-200 pb-4">
              <h4 class="font-medium text-gray-800 mb-2">配额会过期吗？</h4>
              <p class="text-gray-600 text-sm">不会，您购买的配额永久有效，可以随时使用。</p>
            </div>
            
            <div class="border-b border-gray-200 pb-4">
              <h4 class="font-medium text-gray-800 mb-2">生成的临时邮箱有效期多长？</h4>
              <p class="text-gray-600 text-sm">每个临时邮箱有效期为30天，在此期间可以正常接收邮件。</p>
            </div>
            
            <div class="border-b border-gray-200 pb-4">
              <h4 class="font-medium text-gray-800 mb-2">支持哪些支付方式？</h4>
              <p class="text-gray-600 text-sm">目前支持微信支付、支付宝等主流支付方式。</p>
            </div>
            
            <div>
              <h4 class="font-medium text-gray-800 mb-2">购买后如何使用？</h4>
              <p class="text-gray-600 text-sm">购买成功后配额会自动充值到您的账户，在首页即可使用配额生成临时邮箱。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '../../stores/user'

const router = useRouter()
const userStore = useUserStore()

// 处理购买
const handlePurchase = async (plan: string) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再购买配额')
    router.push('/login')
    return
  }

  try {
    await ElMessageBox.confirm(
      '在线支付功能正在开发中，敬请期待！您可以通过兑换码获取配额。',
      '功能开发中',
      {
        confirmButtonText: '了解',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
  } catch (error) {
    // 用户取消
  }
}

// 组件挂载时
onMounted(async () => {
  // 如果用户已登录，获取用户信息
  if (userStore.isLoggedIn) {
    await userStore.fetchProfile()
  }
})
</script>

<style scoped>
.el-button {
  height: 40px;
  font-weight: 500;
}

.border-2 {
  transition: border-color 0.3s ease;
}
</style>
