<template>
  <div class="bento-container">
    <!-- Login Form -->
    <LoginForm
      v-if="!adminStore.isLoggedIn"
      @login="handleLogin"
    />

    <!-- Admin Dashboard -->
    <AdminDashboard
      v-else
      :auth-token="adminStore.authToken"
      @logout="handleLogout"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import LoginForm from './components/LoginForm.vue'
import AdminDashboard from './components/AdminDashboard.vue'
import { EmailService } from '../../services/tempmail'
import { useAdminStore } from '../../stores/admin'

// 使用 admin store 和服务
const adminStore = useAdminStore()
const emailService = new EmailService()

// 方法
const handleLogin = async (loginData: { username: string; password: string }) => {
  try {
    const result = await emailService.adminLogin(loginData.username, loginData.password)

    if (result.success && result.token) {
      adminStore.login(result.token)
      ElMessage.success('登录成功')
    } else {
      ElMessage.error(result.message || '登录失败')
    }
  } catch (error) {
    console.error('Login failed:', error)
    ElMessage.error('登录失败，请检查网络连接')
  }
}

const handleLogout = () => {
  adminStore.logout()
  ElMessage.info('已退出登录')
}

// 生命周期
onMounted(() => {
  // 登录状态现在由 Pinia 持久化自动管理
  console.log('Admin panel mounted, login status:', adminStore.isAuthenticated)
})
</script>
