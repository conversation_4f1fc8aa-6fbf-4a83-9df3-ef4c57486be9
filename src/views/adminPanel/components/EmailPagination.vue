<template>
  <div class="flex justify-center mt-8">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="$emit('size-change', $event)"
      @current-change="$emit('current-change', $event)"
      :prev-text="'上一页'"
      :next-text="'下一页'"
      :page-size-text="'条/页'"
      :total-text="'共 {total} 条'"
      :jumper-text="'前往'"
      :page-text="'页'"
      class="!bg-white !rounded-xl !p-4 !shadow-sm"
    />
  </div>
</template>

<script setup lang="ts">
// 定义 props
defineProps<{
  total: number
}>()

// 定义 v-model
const currentPage = defineModel<number>('currentPage', { required: true })
const pageSize = defineModel<number>('pageSize', { required: true })

// 定义 emits
defineEmits<{
  'size-change': [size: number]
  'current-change': [page: number]
}>()
</script>
