<template>
  <div class="min-h-screen bg-gray-100">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
      <div class="px-6 py-4 flex justify-between items-center">
        <div class="flex items-center space-x-3">
          <i class="fas fa-shield-alt text-blue-600 text-2xl"></i>
          <h1 class="text-xl font-bold text-gray-800">管理后台</h1>
        </div>

        <div class="flex items-center space-x-4">
          <span class="text-gray-600 text-sm">管理员</span>
          <el-button @click="$emit('logout')" type="danger" plain size="small">
            <i class="fas fa-sign-out-alt mr-1"></i>
            退出登录
          </el-button>
        </div>
      </div>
    </header>

    <!-- 主要布局 -->
    <div class="flex h-[calc(100vh-73px)]">
      <!-- 左侧菜单栏 -->
      <aside class="w-64 bg-white shadow-sm border-r border-gray-200">
        <nav class="p-4">
          <ul class="space-y-2">
            <li>
              <button
                @click="activeTab = 'emails'"
                :class="[
                  'w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors',
                  activeTab === 'emails'
                    ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-500'
                    : 'text-gray-700 hover:bg-gray-50'
                ]"
              >
                <i class="fas fa-envelope mr-3"></i>
                邮箱管理
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'users'"
                :class="[
                  'w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors',
                  activeTab === 'users'
                    ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-500'
                    : 'text-gray-700 hover:bg-gray-50'
                ]"
              >
                <i class="fas fa-users mr-3"></i>
                用户管理
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'redemption'"
                :class="[
                  'w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors',
                  activeTab === 'redemption'
                    ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-500'
                    : 'text-gray-700 hover:bg-gray-50'
                ]"
              >
                <i class="fas fa-ticket-alt mr-3"></i>
                兑换码管理
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'announcements'"
                :class="[
                  'w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors',
                  activeTab === 'announcements'
                    ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-500'
                    : 'text-gray-700 hover:bg-gray-50'
                ]"
              >
                <i class="fas fa-bullhorn mr-3"></i>
                公告管理
              </button>
            </li>
          </ul>
        </nav>
      </aside>

      <!-- 右侧内容区域 -->
      <main class="flex-1 overflow-auto">
        <div class="p-6">
          <!-- 邮箱管理 -->
          <div v-if="activeTab === 'emails'" class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
              <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-envelope mr-2 text-blue-600"></i>
                邮箱管理
              </h2>
            </div>

            <div class="p-6">
              <EmailListHeader
                :total-emails="totalEmails"
                :selected-emails="selectedEmails"
                :list-loading="listLoading"
                :batch-delete-loading="batchDeleteLoading"
                @generate="showGenerateDialog"
                @custom-generate="showCustomGenerateDialog"
                @batch-delete="handleBatchDelete"
                @refresh="refreshEmailList"
              />

              <EmailTable
                :email-list="emailList"
                :list-loading="listLoading"
                @selection-change="handleSelectionChange"
                @copy-email="copyEmailAddress"
                @copy-secret="copySecret"
                @use-email="handleUseEmail"
                @edit-email="handleEditEmail"
                @delete-email="handleDeleteEmail"
                @update-cell="handleUpdateCell"
              />

              <!-- 分页 -->
              <EmailPagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :total="totalEmails"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>

          <!-- 用户管理 -->
          <div v-else-if="activeTab === 'users'" class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
              <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-users mr-2 text-blue-600"></i>
                用户管理
              </h2>
            </div>
            <UserManagement :auth-token="authToken" />
          </div>

          <!-- 兑换码管理 -->
          <div v-else-if="activeTab === 'redemption'" class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
              <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-ticket-alt mr-2 text-blue-600"></i>
                兑换码管理
              </h2>
            </div>
            <RedemptionCodeManagement :auth-token="authToken" />
          </div>

          <!-- 系统公告 -->
          <div v-else-if="activeTab === 'announcements'" class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
              <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-bullhorn mr-2 text-blue-600"></i>
                系统公告
              </h2>
            </div>
            <AnnouncementManagement :auth-token="authToken" />
          </div>
        </div>
      </main>
    </div>

    <!-- 对话框组件 -->
    <EditEmailDialog
      v-model="editDialogVisible"
      :edit-form="editForm"
      :update-loading="updateLoading"
      @update="handleUpdateEmail"
      @close="handleEditDialogClose"
    />

    <BatchGenerateDialog
      v-model="generateDialogVisible"
      :generate-form="generateForm"
      :generate-loading="generateLoading"
      @generate="handleBatchGenerate"
    />

    <CustomGenerateDialog
      v-model="customGenerateDialogVisible"
      :custom-generate-form="customGenerateForm"
      :custom-generate-loading="customGenerateLoading"
      @generate="handleCustomGenerate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import EmailListHeader from './EmailListHeader.vue'
import EmailTable from './EmailTable.vue'
import EmailPagination from './EmailPagination.vue'
import EditEmailDialog from './EditEmailDialog.vue'
import BatchGenerateDialog from './BatchGenerateDialog.vue'
import CustomGenerateDialog from './CustomGenerateDialog.vue'
import UserManagement from './UserManagement.vue'
import RedemptionCodeManagement from './RedemptionCodeManagement.vue'
import AnnouncementManagement from './AnnouncementManagement.vue'
import { EmailService, type EmailData, type BatchGenerateRequest, type CustomGenerateRequest } from '../../../services/tempmail'

// 定义 props
const props = defineProps<{
  authToken: string
}>()

// 定义 emits
defineEmits<{
  logout: []
}>()

// 响应式数据
const activeTab = ref('emails')
const emailService = new EmailService()
const emailList = ref<EmailData[]>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const totalEmails = ref(0)

// 多选相关
const selectedEmails = ref<EmailData[]>([])
const batchDeleteLoading = ref(false)

// 加载状态
const listLoading = ref(false)

// 编辑相关
const editDialogVisible = ref(false)
const updateLoading = ref(false)
const editForm = ref({
  id: 0,
  mail: '',
  domain: '',
  type: '',
  relation: '',
  remark: '',
  expireDays: undefined as number | undefined,
  currentExpire: ''
})

// 批量生成对话框
const generateDialogVisible = ref(false)
const generateLoading = ref(false)
const generateForm = ref({
  count: 10,
  domain: '',
  type: '',
  relation: '',
  remark: '',
  expireDays: 30,
  prefix: ''
})

// 自定义生成对话框
const customGenerateDialogVisible = ref(false)
const customGenerateLoading = ref(false)
const customGenerateForm = ref({
  customName: '',
  domain: '',
  type: '',
  relation: '',
  remark: '',
  expireDays: 30
})

// 方法
const showGenerateDialog = () => {
  generateDialogVisible.value = true
}

const showCustomGenerateDialog = () => {
  customGenerateDialogVisible.value = true
}

const refreshEmailList = async () => {
  if (!props.authToken) return

  listLoading.value = true
  try {
    const result = await emailService.getEmailList(props.authToken, currentPage.value, pageSize.value)

    if (result.success && result.emails) {
      emailList.value = result.emails
      totalEmails.value = result.total || 0
    } else {
      ElMessage.error(result.message || '获取邮箱列表失败')
    }
  } catch (error) {
    console.error('Refresh email list failed:', error)
    ElMessage.error('获取邮箱列表失败，请检查网络连接')
  } finally {
    listLoading.value = false
  }
}

const handleSelectionChange = (selection: EmailData[]) => {
  selectedEmails.value = selection
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  refreshEmailList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  refreshEmailList()
}

const handleBatchDelete = async () => {
  if (selectedEmails.value.length === 0) {
    ElMessage.warning('请选择要删除的邮箱')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedEmails.value.length} 个邮箱吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    batchDeleteLoading.value = true
    const emailIds = selectedEmails.value.map(email => email.id!).filter(id => id)

    const result = await emailService.batchDeleteEmails(props.authToken, emailIds)

    if (result.success) {
      ElMessage.success(`成功删除 ${result.deletedCount || emailIds.length} 个邮箱`)
      selectedEmails.value = []
      await refreshEmailList()
    } else {
      ElMessage.error(result.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Batch delete failed:', error)
      ElMessage.error('批量删除失败，请检查网络连接')
    }
  } finally {
    batchDeleteLoading.value = false
  }
}

const handleDeleteEmail = async (email: EmailData) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除邮箱 ${email.mail}@${email.domain} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const result = await emailService.deleteEmail(props.authToken, email.id || 0)

    if (result.success) {
      ElMessage.success('邮箱删除成功')
      await refreshEmailList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete email failed:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }
}

const handleEditEmail = (email: EmailData) => {
  editForm.value = {
    id: email.id || 0,
    mail: email.mail,
    domain: email.domain,
    type: email.type || '',
    relation: email.relation || '',
    remark: email.remark || '',
    expireDays: undefined,
    currentExpire: email.expire
  }
  editDialogVisible.value = true
}

const handleEditDialogClose = () => {
  editForm.value = {
    id: 0,
    mail: '',
    domain: '',
    type: '',
    relation: '',
    remark: '',
    expireDays: undefined,
    currentExpire: ''
  }
  editDialogVisible.value = false
}

const handleUpdateEmail = async (updateData: { type?: string; relation?: string; remark?: string; expireDays?: number }) => {
  updateLoading.value = true
  try {
    const result = await emailService.updateEmail(props.authToken, editForm.value.id, updateData)

    if (result.success) {
      ElMessage.success('邮箱信息更新成功')
      editDialogVisible.value = false
      await refreshEmailList()
    } else {
      ElMessage.error(result.message || '更新失败')
    }
  } catch (error) {
    console.error('Update email failed:', error)
    ElMessage.error('更新失败，请检查网络连接')
  } finally {
    updateLoading.value = false
  }
}

// 处理表格单元格更新
const handleUpdateCell = async (email: EmailData, field: string, value: string) => {
  try {
    const updateData: { type?: string; relation?: string; remark?: string } = {}
    updateData[field as keyof typeof updateData] = value

    const result = await emailService.updateEmail(props.authToken, email.id!, updateData)

    if (result.success) {
      ElMessage.success('更新成功')
      await refreshEmailList()
    } else {
      ElMessage.error(result.message || '更新失败')
    }
  } catch (error) {
    console.error('Update cell failed:', error)
    ElMessage.error('更新失败，请检查网络连接')
  }
}

const handleBatchGenerate = async (request: BatchGenerateRequest) => {
  generateLoading.value = true
  try {
    const result = await emailService.batchGenerateEmails(props.authToken, request)

    if (result.success) {
      ElMessage.success(`成功生成 ${result.emails?.length || 0} 个邮箱`)
      await refreshEmailList()
      generateDialogVisible.value = false
    } else {
      ElMessage.error(result.message || '生成失败')
    }
  } catch (error) {
    console.error('Batch generate failed:', error)
    ElMessage.error('生成失败，请检查网络连接')
  } finally {
    generateLoading.value = false
  }
}

const handleCustomGenerate = async (request: CustomGenerateRequest) => {
  customGenerateLoading.value = true
  try {
    const result = await emailService.customGenerateEmail(props.authToken, request)

    if (result.success) {
      ElMessage.success(`成功生成邮箱 ${result.email?.mail}@${result.email?.domain}`)
      await refreshEmailList()
      customGenerateDialogVisible.value = false
    } else {
      ElMessage.error(result.message || '生成失败')
    }
  } catch (error) {
    console.error('Custom generate failed:', error)
    ElMessage.error('生成失败，请检查网络连接')
  } finally {
    customGenerateLoading.value = false
  }
}

// 工具方法
const copySecret = async (secret: string) => {
  try {
    await navigator.clipboard.writeText(secret)
    ElMessage.success('授权密钥已复制到剪贴板')
  } catch (error) {
    // 降级处理：创建临时输入框
    const textArea = document.createElement('textarea')
    textArea.value = secret
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('授权密钥已复制到剪贴板')
  }
}

const copyEmailAddress = async (email: EmailData) => {
  const emailAddress = `${email.mail}@${email.domain}`
  try {
    await navigator.clipboard.writeText(emailAddress)
    ElMessage.success(`已复制邮箱地址：${emailAddress}`)
  } catch (error) {
    // 降级处理：创建临时输入框
    const textArea = document.createElement('textarea')
    textArea.value = emailAddress
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success(`已复制邮箱地址：${emailAddress}`)
  }
}

const handleUseEmail = (email: EmailData) => {
  // 构建查询参数，通过URL传递邮箱信息
  const params = new URLSearchParams({
    secret: email.secret || '',
    email: `${email.mail}@${email.domain}`
  })

  // 跳转到用户页面，通过URL参数传递信息
  window.open(`/apikey_mail?${params.toString()}`, '_blank')

  ElMessage.success(`已打开邮箱 ${email.mail}@${email.domain} 的查看页面`)
}

// 生命周期
onMounted(() => {
  refreshEmailList()
})
</script>
