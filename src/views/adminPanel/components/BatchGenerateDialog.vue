<template>
  <el-dialog
    v-model="visible"
    title="批量生成邮箱"
    width="600px"
    :close-on-click-modal="false"
    class="!rounded-2xl !overflow-hidden"
  >
    <el-form :model="generateForm" label-width="120px" :label-position="'left'" class="!p-4">
      <el-form-item label="生成数量" required>
        <el-input-number
          v-model="generateForm.count"
          :min="1"
          :max="100"
          placeholder="1-100"
          style="width: 100%"
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-info-circle mr-1"></i>
          一次最多可生成100个邮箱
        </div>
      </el-form-item>

      <el-form-item label="域名" required>
        <el-select
          v-model="generateForm.domain"
          placeholder="请选择域名"
          style="width: 100%"
          class="!rounded-xl"
        >
          <el-option label="ofun.my" value="ofun.my" />
          <el-option label="ofun.io" value="ofun.io" />
          <el-option label="ofun.net" value="ofun.net" />
        </el-select>
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-globe mr-1"></i>
          选择邮箱的域名后缀
        </div>
      </el-form-item>

      <el-form-item label="邮箱前缀">
        <el-input
          v-model="generateForm.prefix"
          placeholder="最多8位，可为空"
          maxlength="8"
          show-word-limit
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-tag mr-1"></i>
          留空则完全随机生成，填写则以此为前缀
        </div>
      </el-form-item>

      <el-form-item label="类型" required>
        <el-input
          v-model="generateForm.type"
          placeholder="如：闲鱼、淘宝等"
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-bookmark mr-1"></i>
          用于标识邮箱的用途或平台
        </div>
      </el-form-item>

      <el-form-item label="绑定关系">
        <el-input
          v-model="generateForm.relation"
          placeholder="绑定关系内容（可选）"
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-link mr-1"></i>
          可选字段，用于关联其他信息
        </div>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="generateForm.remark"
          placeholder="备注内容（可选）"
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-comment mr-1"></i>
          可选字段，用于添加备注信息
        </div>
      </el-form-item>

      <el-form-item label="有效期（天）" required>
        <el-input-number
          v-model="generateForm.expireDays"
          :min="1"
          :max="365"
          placeholder="1-365"
          style="width: 100%"
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-calendar-alt mr-1"></i>
          邮箱的有效期，过期后将无法接收邮件
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer flex space-x-3 p-4">
        <el-button @click="handleClose" class="!rounded-xl !px-6 !py-2">取消</el-button>
        <el-button
          type="primary"
          @click="handleGenerate"
          :disabled="!canGenerate || generateLoading"
          :loading="generateLoading"
          class="!bg-gradient-to-r !from-green-600 !to-green-700 !border-none !rounded-xl !px-6 !py-2 !font-semibold hover:!from-green-700 hover:!to-green-800"
        >
          {{ generateLoading ? '生成中...' : '开始生成' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { BatchGenerateRequest } from '../../../services/tempmail'

// 定义 props
const props = defineProps<{
  generateForm: {
    count: number
    domain: string
    type: string
    relation: string
    remark: string
    expireDays: number
    prefix: string
  }
  generateLoading: boolean
}>()

// 定义 v-model
const visible = defineModel<boolean>({ required: true })

// 定义 emits
const emit = defineEmits<{
  generate: [request: BatchGenerateRequest]
}>()

// 计算属性
const canGenerate = computed(() => {
  return props.generateForm.count > 0 &&
         props.generateForm.domain &&
         props.generateForm.type &&
         props.generateForm.expireDays > 0
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleGenerate = () => {
  if (!canGenerate.value) {
    ElMessage.error('请填写完整的生成信息')
    return
  }

  const request: BatchGenerateRequest = {
    count: props.generateForm.count,
    domain: props.generateForm.domain,
    type: props.generateForm.type,
    relation: props.generateForm.relation || undefined,
    expireDays: props.generateForm.expireDays,
    prefix: props.generateForm.prefix || undefined
  }

  emit('generate', request)
}
</script>
