<template>
  <el-dialog
    v-model="visible"
    title="生成指定名称邮箱"
    width="500px"
    :close-on-click-modal="false"
    class="!rounded-2xl !overflow-hidden"
  >
    <el-form :model="customGenerateForm" label-width="120px" :label-position="'left'" class="!p-4">
      <el-form-item label="邮箱名称" required>
        <el-input
          v-model="customGenerateForm.customName"
          placeholder="3-20位字母、数字、下划线、连字符"
          maxlength="20"
          show-word-limit
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-user mr-1"></i>
          只能包含字母、数字、下划线和连字符，长度3-20位
        </div>
      </el-form-item>

      <el-form-item label="域名" required>
        <el-select
          v-model="customGenerateForm.domain"
          placeholder="请选择域名"
          style="width: 100%"
          class="!rounded-xl"
        >
          <el-option label="ofun.my" value="ofun.my" />
          <el-option label="ofun.io" value="ofun.io" />
          <el-option label="ofun.net" value="ofun.net" />
        </el-select>
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-globe mr-1"></i>
          选择邮箱的域名后缀
        </div>
      </el-form-item>

      <el-form-item label="类型" required>
        <el-input
          v-model="customGenerateForm.type"
          placeholder="如：闲鱼、淘宝等"
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-bookmark mr-1"></i>
          用于标识邮箱的用途或平台
        </div>
      </el-form-item>

      <el-form-item label="绑定关系">
        <el-input
          v-model="customGenerateForm.relation"
          placeholder="绑定关系内容（可选）"
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-link mr-1"></i>
          可选字段，用于关联其他信息
        </div>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="customGenerateForm.remark"
          placeholder="备注内容（可选）"
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-comment mr-1"></i>
          可选字段，用于添加备注信息
        </div>
      </el-form-item>

      <el-form-item label="有效期（天）" required>
        <el-input-number
          v-model="customGenerateForm.expireDays"
          :min="1"
          :max="365"
          placeholder="1-365"
          style="width: 100%"
          class="!rounded-xl"
        />
        <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded-lg">
          <i class="fas fa-calendar-alt mr-1"></i>
          邮箱的有效期，过期后将无法接收邮件
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer flex space-x-3 p-4">
        <el-button @click="handleClose" class="!rounded-xl !px-6 !py-2">取消</el-button>
        <el-button
          type="primary"
          @click="handleGenerate"
          :disabled="!canCustomGenerate || customGenerateLoading"
          :loading="customGenerateLoading"
          class="!bg-gradient-to-r !from-blue-600 !to-blue-700 !border-none !rounded-xl !px-6 !py-2 !font-semibold hover:!from-blue-700 hover:!to-blue-800"
        >
          {{ customGenerateLoading ? '生成中...' : '生成邮箱' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { CustomGenerateRequest } from '../../../services/tempmail'

// 定义 props
const props = defineProps<{
  customGenerateForm: {
    customName: string
    domain: string
    type: string
    relation: string
    remark: string
    expireDays: number
  }
  customGenerateLoading: boolean
}>()

// 定义 v-model
const visible = defineModel<boolean>({ required: true })

// 定义 emits
const emit = defineEmits<{
  generate: [request: CustomGenerateRequest]
}>()

// 计算属性
const canCustomGenerate = computed(() => {
  return props.customGenerateForm.customName &&
         props.customGenerateForm.domain &&
         props.customGenerateForm.type &&
         props.customGenerateForm.expireDays > 0 &&
         /^[a-zA-Z0-9_-]{3,20}$/.test(props.customGenerateForm.customName)
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleGenerate = () => {
  if (!canCustomGenerate.value) {
    ElMessage.error('请填写完整的邮箱信息')
    return
  }

  const request: CustomGenerateRequest = {
    customName: props.customGenerateForm.customName,
    domain: props.customGenerateForm.domain,
    type: props.customGenerateForm.type,
    relation: props.customGenerateForm.relation || undefined,
    expireDays: props.customGenerateForm.expireDays
  }

  emit('generate', request)
}
</script>
