<template>
  <div class="flex items-center justify-between mb-8">
    <h3 class="text-2xl font-bold bento-text-primary flex items-center">
      <i class="fas fa-list bento-accent text-3xl mr-4"></i>
      邮箱列表
      <span class="bento-tag ml-4">共 {{ totalEmails }} 个邮箱</span>
    </h3>
    <div class="flex items-center space-x-3 flex-wrap">
      <button
        @click="$emit('generate')"
        class="bento-button bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 flex items-center"
      >
        <i class="fas fa-layer-group mr-2"></i>
        批量生成邮箱
      </button>
      <button
        @click="$emit('custom-generate')"
        class="bento-button bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 flex items-center"
      >
        <i class="fas fa-edit mr-2"></i>
        生成指定邮箱
      </button>
      <button
        v-if="selectedEmails.length > 0"
        @click="$emit('batch-delete')"
        :disabled="batchDeleteLoading"
        class="bento-button bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
      >
        <i class="fas fa-trash-alt mr-2" :class="{ 'animate-spin': batchDeleteLoading }"></i>
        批量删除 ({{ selectedEmails.length }})
      </button>
      <button
        @click="$emit('refresh')"
        :disabled="listLoading"
        class="bento-button disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
      >
        <i class="fas fa-sync-alt mr-2" :class="{ 'animate-spin': listLoading }"></i>
        刷新列表
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { EmailData } from '../../../services/tempmail'

// 定义 props
defineProps<{
  totalEmails: number
  selectedEmails: EmailData[]
  listLoading: boolean
  batchDeleteLoading: boolean
}>()

// 定义 emits
defineEmits<{
  generate: []
  'custom-generate': []
  'batch-delete': []
  refresh: []
}>()
</script>
