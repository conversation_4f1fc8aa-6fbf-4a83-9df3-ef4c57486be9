<template>
  <el-dialog
    v-model="visible"
    title="编辑邮箱信息"
    width="500px"
    :before-close="handleClose"
    class="!rounded-2xl !overflow-hidden"
  >
    <el-form
      ref="editFormRef"
      :model="editForm"
      :rules="editFormRules"
      label-width="100px"
      class="!p-4"
    >
      <el-form-item label="邮箱地址">
        <el-input
          :value="`${editForm.mail}@${editForm.domain}`"
          disabled
          class="font-mono !rounded-xl"
        />
      </el-form-item>

      <el-form-item label="类型" prop="type">
        <el-input v-model="editForm.type" placeholder="请输入邮箱类型" class="!rounded-xl" />
      </el-form-item>

      <el-form-item label="绑定关系" prop="relation">
        <el-input v-model="editForm.relation" placeholder="请输入绑定关系（可选）" class="!rounded-xl" />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="editForm.remark" placeholder="请输入备注（可选）" class="!rounded-xl" />
      </el-form-item>

      <el-form-item label="延长天数" prop="expireDays">
        <el-input-number
          v-model="editForm.expireDays"
          :min="1"
          :max="365"
          placeholder="延长天数"
          style="width: 100%"
          class="!rounded-xl"
        />
        <div class="text-sm text-gray-500 mt-2 p-3 bg-gray-50 rounded-xl">
          当前过期时间：{{ formatDate(editForm.currentExpire) }}
          <span v-if="editForm.expireDays" class="ml-2 text-blue-600 font-medium">
            → 延长后：{{ formatDate(getNewExpireDate(editForm.currentExpire, editForm.expireDays)) }}
          </span>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer flex space-x-3 p-4">
        <el-button @click="handleClose" class="!rounded-xl !px-6 !py-2">取消</el-button>
        <el-button
          type="primary"
          @click="handleUpdate"
          :loading="updateLoading"
          class="!bg-gradient-to-r !from-blue-600 !to-blue-700 !border-none !rounded-xl !px-6 !py-2 !font-semibold hover:!from-blue-700 hover:!to-blue-800"
        >
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance } from 'element-plus'

// 定义 props
const props = defineProps<{
  editForm: {
    id: number
    mail: string
    domain: string
    type: string
    relation: string
    remark: string
    expireDays: number | undefined
    currentExpire: string
  }
  updateLoading: boolean
}>()

// 定义 v-model
const visible = defineModel<boolean>({ required: true })

// 定义 emits
const emit = defineEmits<{
  update: [updateData: { type?: string; relation?: string; remark?: string; expireDays?: number }]
  close: []
}>()

// 响应式数据
const editFormRef = ref<FormInstance>()

const editFormRules = {
  type: [
    { required: true, message: '请输入邮箱类型', trigger: 'blur' }
  ]
}

// 方法
const handleClose = () => {
  editFormRef.value?.resetFields()
  emit('close')
}

const handleUpdate = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()

    const updateData: { type?: string; relation?: string; remark?: string; expireDays?: number } = {}

    if (props.editForm.type) updateData.type = props.editForm.type
    if (props.editForm.relation) updateData.relation = props.editForm.relation
    if (props.editForm.remark !== undefined) updateData.remark = props.editForm.remark
    if (props.editForm.expireDays) updateData.expireDays = props.editForm.expireDays

    emit('update', updateData)
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

// 工具方法
const formatDate = (dateStr: string) => {
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch {
    return dateStr
  }
}

const getNewExpireDate = (currentExpire: string, days: number) => {
  const date = new Date(currentExpire)
  date.setDate(date.getDate() + days)
  return date.toISOString()
}
</script>
