<template>
  <div class="space-y-6">
    <!-- 头部 -->
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-semibold text-gray-800">用户管理</h2>
      <div class="flex items-center space-x-4">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索用户..."
          class="w-64"
          clearable
        >
          <template #prefix>
            <i class="fas fa-search text-gray-400"></i>
          </template>
        </el-input>
        <el-button @click="refreshUsers" :loading="loading">
          <i class="fas fa-refresh mr-1"></i>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="bg-blue-100 p-3 rounded-full">
            <i class="fas fa-users text-blue-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">总用户数</p>
            <p class="text-2xl font-semibold text-gray-800">{{ stats.totalUsers }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="bg-green-100 p-3 rounded-full">
            <i class="fas fa-user-check text-green-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">活跃用户</p>
            <p class="text-2xl font-semibold text-gray-800">{{ stats.activeUsers }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="bg-yellow-100 p-3 rounded-full">
            <i class="fas fa-coins text-yellow-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">总配额</p>
            <p class="text-2xl font-semibold text-gray-800">{{ stats.totalCredits }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="bg-purple-100 p-3 rounded-full">
            <i class="fab fa-github text-purple-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">GitHub 用户</p>
            <p class="text-2xl font-semibold text-gray-800">{{ stats.githubUsers }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white rounded-lg shadow">
      <div class="p-6">
        <el-table
          :data="filteredUsers"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          
          <el-table-column label="用户信息" min-width="200">
            <template #default="{ row }">
              <div class="flex items-center space-x-3">
                <el-avatar :size="40" :src="row.avatar_url">
                  <i class="fas fa-user"></i>
                </el-avatar>
                <div>
                  <p class="font-medium text-gray-800">
                    {{ row.display_name || row.username || '未设置' }}
                  </p>
                  <p class="text-sm text-gray-500">{{ row.email }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="配额信息" width="120">
            <template #default="{ row }">
              <div class="text-center">
                <p class="font-semibold text-blue-600">{{ row.balance || 0 }}</p>
                <p class="text-xs text-gray-500">
                  获得: {{ row.total_earned || 0 }} | 消费: {{ row.total_spent || 0 }}
                </p>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.status === 'active' ? 'success' : 
                      row.status === 'suspended' ? 'warning' : 'danger'"
                size="small"
              >
                {{ row.status === 'active' ? '正常' : 
                   row.status === 'suspended' ? '暂停' : '删除' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="注册方式" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.github_id ? 'primary' : 'default'"
                size="small"
              >
                {{ row.github_id ? 'GitHub' : '邮箱' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="注册时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="space-x-2">
                <el-button
                  size="small"
                  type="primary"
                  @click="adjustCredits(row)"
                >
                  调整配额
                </el-button>
                <el-button
                  size="small"
                  :type="row.status === 'active' ? 'warning' : 'success'"
                  @click="toggleUserStatus(row)"
                >
                  {{ row.status === 'active' ? '暂停' : '激活' }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex justify-center mt-6">
          <el-pagination
            v-model:current-page="pagination.page"
            :page-size="pagination.limit"
            :total="pagination.total"
            layout="total, prev, pager, next, jumper"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 调整配额对话框 -->
    <el-dialog
      v-model="creditDialog.visible"
      title="调整用户配额"
      width="400px"
    >
      <el-form :model="creditDialog.form" label-width="80px">
        <el-form-item label="用户">
          <p class="text-gray-800">{{ creditDialog.user?.email }}</p>
        </el-form-item>
        <el-form-item label="当前配额">
          <p class="text-blue-600 font-semibold">{{ creditDialog.user?.balance || 0 }}</p>
        </el-form-item>
        <el-form-item label="调整数量" required>
          <el-input-number
            v-model="creditDialog.form.amount"
            :min="-999999"
            :max="999999"
            placeholder="正数增加，负数减少"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="调整原因" required>
          <el-input
            v-model="creditDialog.form.description"
            type="textarea"
            placeholder="请输入调整原因"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="creditDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="creditDialog.loading"
          @click="confirmAdjustCredits"
        >
          确认调整
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDateToChinaTime } from '../../../utils/dateFormat'

const props = defineProps<{
  authToken: string
}>()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const users = ref<any[]>([])
const stats = ref({
  totalUsers: 0,
  activeUsers: 0,
  totalCredits: 0,
  githubUsers: 0
})

const pagination = ref({
  page: 1,
  limit: 20,
  total: 0
})

const creditDialog = ref({
  visible: false,
  loading: false,
  user: null as any,
  form: {
    amount: 0,
    description: ''
  }
})

// 计算属性
const filteredUsers = computed(() => {
  if (!searchKeyword.value) return users.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return users.value.filter(user => 
    user.email.toLowerCase().includes(keyword) ||
    (user.username && user.username.toLowerCase().includes(keyword)) ||
    (user.display_name && user.display_name.toLowerCase().includes(keyword))
  )
})

// API 基础 URL
const apiUrl = 'https://ofun-email-system.htmljs.workers.dev/api'

// 获取认证头
const getAuthHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${props.authToken}`
})

// 格式化日期
const formatDate = (dateStr: string) => {
  return formatDateToChinaTime(dateStr, 'YYYY-MM-DD HH:mm')
}

// 获取用户列表
const fetchUsers = async (page = 1) => {
  loading.value = true
  try {
    const response = await fetch(`${apiUrl}/admin/users?page=${page}&limit=${pagination.value.limit}`, {
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      users.value = result.users || []
      pagination.value = { ...pagination.value, ...result.pagination }
      
      // 计算统计数据
      stats.value = {
        totalUsers: result.pagination.total,
        activeUsers: users.value.filter(u => u.status === 'active').length,
        totalCredits: users.value.reduce((sum, u) => sum + (u.balance || 0), 0),
        githubUsers: users.value.filter(u => u.github_id).length
      }
    } else {
      ElMessage.error(result.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('Fetch users error:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新用户列表
const refreshUsers = () => {
  fetchUsers(pagination.value.page)
}

// 处理分页变化
const handlePageChange = (page: number) => {
  fetchUsers(page)
}

// 调整配额
const adjustCredits = (user: any) => {
  creditDialog.value.user = user
  creditDialog.value.form = {
    amount: 0,
    description: ''
  }
  creditDialog.value.visible = true
}

// 确认调整配额
const confirmAdjustCredits = async () => {
  if (!creditDialog.value.form.amount || !creditDialog.value.form.description) {
    ElMessage.warning('请填写调整数量和原因')
    return
  }

  creditDialog.value.loading = true
  try {
    const response = await fetch(`${apiUrl}/admin/users/credits`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        userId: creditDialog.value.user.id,
        amount: creditDialog.value.form.amount,
        description: creditDialog.value.form.description
      })
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('配额调整成功')
      creditDialog.value.visible = false
      refreshUsers()
    } else {
      ElMessage.error(result.message || '配额调整失败')
    }
  } catch (error) {
    console.error('Adjust credits error:', error)
    ElMessage.error('配额调整失败')
  } finally {
    creditDialog.value.loading = false
  }
}

// 切换用户状态
const toggleUserStatus = async (user: any) => {
  const newStatus = user.status === 'active' ? 'suspended' : 'active'
  const action = newStatus === 'active' ? '激活' : '暂停'
  
  try {
    await ElMessageBox.confirm(`确定要${action}用户 ${user.email} 吗？`, `${action}用户`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // TODO: 实现用户状态切换 API
    ElMessage.info('用户状态切换功能开发中')
  } catch (error) {
    // 用户取消操作
  }
}

// 组件挂载时
onMounted(() => {
  fetchUsers()
})
</script>
