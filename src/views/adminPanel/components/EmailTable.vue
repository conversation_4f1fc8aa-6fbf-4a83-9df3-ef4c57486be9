<template>
  <div class="bento-mail-list" style="max-height: 60vh; overflow-y: auto;">
    <el-table
      v-loading="listLoading"
      :data="emailList"
      @selection-change="$emit('selection-change', $event)"
      style="width: 100%"
      empty-text="暂无邮箱数据，点击上方批量生成按钮创建邮箱"
      class="!rounded-xl !overflow-hidden !shadow-sm"
      height="100%"
      :row-style="{ height: '60px' }"
      :cell-style="{ padding: '16px 12px' }"
    >
      <el-table-column type="selection" width="55" fixed="left" />

      <el-table-column prop="email" label="邮箱地址" min-width="250" fixed="left">
        <template #default="{ row }">
          <div class="flex items-center space-x-1">
            <span
              class="font-mono text-gray-800 font-medium flex-1 cursor-pointer hover:text-blue-600 transition-colors"
              @click="$emit('copy-email', row)"
              title="点击复制邮箱地址"
            >{{ row.mail }}@{{ row.domain }}</span>
            <el-button
              type="primary"
              :icon="CopyDocument"
              @click="$emit('copy-email', row)"
              size="small"
              circle
              title="复制邮箱地址"
              class="!bg-blue-50 !text-blue-600 !border-blue-200 hover:!bg-blue-100 hover:!border-blue-300 !transition-all !duration-200"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <div v-if="editingCell.rowId === row.id && editingCell.field === 'type'">
            <el-input
              v-model="editingCell.value"
              @blur="handleCellEditComplete(row)"
              @keyup.enter="handleCellEditComplete(row)"
              @keyup.esc="handleCellEditCancel"
              size="small"
              ref="editInput"
              class="!rounded-lg"
            />
          </div>
          <span
            v-else
            class="text-gray-700 font-medium cursor-pointer hover:text-blue-600 transition-colors"
            @dblclick="handleCellEdit(row, 'type', row.type)"
            title="双击编辑"
          >{{ row.type }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="relation" label="绑定关系" width="150">
        <template #default="{ row }">
          <span class="font-mono text-gray-600">{{ row.relation || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="remark" label="备注" width="150">
        <template #default="{ row }">
          <div v-if="editingCell.rowId === row.id && editingCell.field === 'remark'">
            <el-input
              v-model="editingCell.value"
              @blur="handleCellEditComplete(row)"
              @keyup.enter="handleCellEditComplete(row)"
              @keyup.esc="handleCellEditCancel"
              size="small"
              ref="editInput"
              class="!rounded-lg"
            />
          </div>
          <span
            v-else
            class="text-gray-600 cursor-pointer hover:text-blue-600 transition-colors"
            @dblclick="handleCellEdit(row, 'remark', row.remark || '')"
            title="双击编辑"
          >{{ row.remark || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="secret" label="授权密钥" min-width="200">
        <template #default="{ row }">
          <div class="flex items-center space-x-2">
            <span class="font-mono text-xs text-gray-600 truncate bg-gray-100 px-2 py-1 rounded-lg">{{ row.secret }}</span>
            <el-button
              type="text"
              :icon="CopyDocument"
              @click="$emit('copy-secret', row.secret)"
              size="small"
              title="复制密钥"
              class="!text-blue-600 hover:!text-blue-700 hover:!bg-blue-50 !rounded-lg !transition-all !duration-200"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="expire" label="过期状态" width="120">
        <template #default="{ row }">
          <el-tag
            :type="isEmailExpired(row.expire) ? 'danger' : 'success'"
            size="small"
            class="!rounded-full !font-medium"
          >
            {{ isEmailExpired(row.expire) ? '已过期' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="expire" label="过期时间" width="180">
        <template #default="{ row }">
          <span :class="{ 'text-red-500 font-semibold': isEmailExpired(row.expire), 'text-gray-700': !isEmailExpired(row.expire) }">
            {{ formatDate(row.expire) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <div class="flex items-center space-x-1">
            <el-tooltip content="用此邮箱接收邮件" placement="top">
              <el-button
                type="success"
                :icon="Message"
                @click="$emit('use-email', row)"
                size="small"
                circle
                class="!bg-green-50 !text-green-600 !border-green-200 hover:!bg-green-100 hover:!border-green-300 !transition-all !duration-200"
              />
            </el-tooltip>
            <el-tooltip content="编辑邮箱信息" placement="top">
              <el-button
                type="primary"
                :icon="Edit"
                @click="$emit('edit-email', row)"
                size="small"
                circle
                class="!bg-blue-50 !text-blue-600 !border-blue-200 hover:!bg-blue-100 hover:!border-blue-300 !transition-all !duration-200"
              />
            </el-tooltip>
            <el-tooltip content="删除邮箱" placement="top">
              <el-button
                type="danger"
                :icon="Delete"
                @click="$emit('delete-email', row)"
                size="small"
                circle
                class="!bg-red-50 !text-red-600 !border-red-200 hover:!bg-red-100 hover:!border-red-300 !transition-all !duration-200"
              />
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { Delete, Edit, CopyDocument, Message } from '@element-plus/icons-vue'
import type { EmailData } from '../../../services/tempmail'

// 定义 props
defineProps<{
  emailList: EmailData[]
  listLoading: boolean
}>()

// 定义 emits
const emit = defineEmits<{
  'selection-change': [selection: EmailData[]]
  'copy-email': [email: EmailData]
  'copy-secret': [secret: string]
  'use-email': [email: EmailData]
  'edit-email': [email: EmailData]
  'delete-email': [email: EmailData]
  'update-cell': [email: EmailData, field: string, value: string]
}>()

// 双击编辑相关状态
const editingCell = ref({
  rowId: null as number | null,
  field: '',
  value: '',
  originalValue: ''
})

const editInput = ref()

// 双击编辑方法
const handleCellEdit = async (row: EmailData, field: string, value: string) => {
  editingCell.value = {
    rowId: row.id!,
    field,
    value,
    originalValue: value
  }

  await nextTick()
  editInput.value?.focus()
}

const handleCellEditComplete = (row: EmailData) => {
  if (editingCell.value.value !== editingCell.value.originalValue) {
    emit('update-cell', row, editingCell.value.field, editingCell.value.value)
  }
  handleCellEditCancel()
}

const handleCellEditCancel = () => {
  editingCell.value = {
    rowId: null,
    field: '',
    value: '',
    originalValue: ''
  }
}

// 工具方法
const isEmailExpired = (expireDate: string) => {
  return new Date() > new Date(expireDate)
}

const formatDate = (dateStr: string) => {
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch {
    return dateStr
  }
}
</script>
