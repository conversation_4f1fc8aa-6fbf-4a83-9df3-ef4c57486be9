<template>
  <div class="bento-card w-250 max-w-lg mx-auto">
    <div class="text-center mb-8">
      <div class="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
        <i class="fas fa-user-shield text-white text-2xl"></i>
      </div>
      <h2 class="text-3xl font-bold bento-text-primary">
        管理员登录
      </h2>
      <p class="bento-text-muted mt-2">请输入管理员账号和密码</p>
    </div>

    <div class="space-y-6">
      <div>
        <label class="block bento-text-secondary text-sm font-semibold mb-3">用户名</label>
        <input
          v-model="loginForm.username"
          type="text"
          placeholder="请输入用户名"
          class="bento-input w-full"
          @keyup.enter="handleLogin"
        />
      </div>

      <div>
        <label class="block bento-text-secondary text-sm font-semibold mb-3">密码</label>
        <input
          v-model="loginForm.password"
          type="password"
          placeholder="请输入密码"
          class="bento-input w-full"
          @keyup.enter="handleLogin"
        />
      </div>

      <button
        @click="handleLogin"
        :disabled="!loginForm.username || !loginForm.password || loginLoading"
        class="bento-button w-full disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
      >
        <i class="fas fa-sign-in-alt mr-2" :class="{ 'animate-spin': loginLoading }"></i>
        {{ loginLoading ? '登录中...' : '登录' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 定义 emits
const emit = defineEmits<{
  login: [loginData: { username: string; password: string }]
}>()

// 响应式数据
const loginLoading = ref(false)
const loginForm = ref({
  username: '',
  password: ''
})

// 方法
const handleLogin = async () => {
  if (!loginForm.value.username || !loginForm.value.password) {
    ElMessage.error('请输入用户名和密码')
    return
  }

  loginLoading.value = true
  try {
    emit('login', { ...loginForm.value })
  } finally {
    loginLoading.value = false
  }
}
</script>
