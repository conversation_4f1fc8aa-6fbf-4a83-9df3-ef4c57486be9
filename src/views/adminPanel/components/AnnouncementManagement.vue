<template>
  <div class="space-y-6">
    <!-- 头部 -->
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-semibold text-gray-800">系统公告管理</h2>
      <div class="flex items-center space-x-4">
        <el-button type="primary" @click="showCreateDialog">
          <i class="fas fa-plus mr-1"></i>
          新建公告
        </el-button>
        <el-button @click="refreshAnnouncements" :loading="loading">
          <i class="fas fa-refresh mr-1"></i>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 公告列表 -->
    <div class="bg-white rounded-lg shadow">
      <div class="p-6">
        <el-table
          :data="announcements"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          
          <el-table-column prop="title" label="标题" min-width="200">
            <template #default="{ row }">
              <div class="flex items-center space-x-2">
                <i :class="[
                  'text-sm',
                  row.type === 'success' ? 'fas fa-check-circle text-green-500' :
                  row.type === 'warning' ? 'fas fa-exclamation-triangle text-yellow-500' :
                  row.type === 'error' ? 'fas fa-times-circle text-red-500' :
                  'fas fa-info-circle text-blue-500'
                ]"></i>
                <span class="font-medium">{{ row.title }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="content" label="内容" min-width="300">
            <template #default="{ row }">
              <div class="text-sm text-gray-600 line-clamp-2">
                {{ row.content }}
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="类型" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.type === 'success' ? 'success' : 
                      row.type === 'warning' ? 'warning' : 
                      row.type === 'error' ? 'danger' : 'primary'"
                size="small"
              >
                {{ getTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.priority > 5 ? 'danger' : row.priority > 0 ? 'warning' : 'default'"
                size="small"
              >
                {{ row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row)"
                size="small"
              >
                {{ getStatusText(row) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="expires_at" label="过期时间" width="160">
            <template #default="{ row }">
              {{ row.expires_at ? formatDate(row.expires_at) : '永不过期' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="space-x-2">
                <el-button
                  size="small"
                  type="primary"
                  @click="editAnnouncement(row)"
                >
                  编辑
                </el-button>
                <el-button
                  size="small"
                  :type="row.is_active ? 'warning' : 'success'"
                  @click="toggleStatus(row)"
                >
                  {{ row.is_active ? '禁用' : '启用' }}
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="deleteAnnouncement(row)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 创建/编辑公告对话框 -->
    <el-dialog
      v-model="announcementDialog.visible"
      :title="announcementDialog.isEdit ? '编辑公告' : '新建公告'"
      width="600px"
    >
      <el-form :model="announcementDialog.form" label-width="100px">
        <el-form-item label="标题" required>
          <el-input
            v-model="announcementDialog.form.title"
            placeholder="请输入公告标题"
          />
        </el-form-item>
        
        <el-form-item label="内容" required>
          <el-input
            v-model="announcementDialog.form.content"
            type="textarea"
            :rows="4"
            placeholder="请输入公告内容"
          />
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select v-model="announcementDialog.form.type" placeholder="选择公告类型">
            <el-option label="信息" value="info" />
            <el-option label="成功" value="success" />
            <el-option label="警告" value="warning" />
            <el-option label="错误" value="error" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-input-number
            v-model="announcementDialog.form.priority"
            :min="0"
            :max="10"
            placeholder="0-10，数字越大优先级越高"
            class="w-full"
          />
          <p class="text-xs text-gray-500 mt-1">数字越大优先级越高，会显示在前面</p>
        </el-form-item>
        
        <el-form-item label="有效期">
          <el-input-number
            v-model="announcementDialog.form.expiresInDays"
            :min="1"
            :max="365"
            placeholder="天数，留空为永不过期"
            class="w-full"
          />
          <p class="text-xs text-gray-500 mt-1">留空表示永不过期</p>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch
            v-model="announcementDialog.form.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="announcementDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="announcementDialog.loading"
          @click="confirmSave"
        >
          {{ announcementDialog.isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDateToChinaTime } from '../../../utils/dateFormat'

const props = defineProps<{
  authToken: string
}>()

// 响应式数据
const loading = ref(false)
const announcements = ref<any[]>([])

const announcementDialog = ref({
  visible: false,
  loading: false,
  isEdit: false,
  editId: null as number | null,
  form: {
    title: '',
    content: '',
    type: 'info',
    priority: 0,
    expiresInDays: null as number | null,
    isActive: true
  }
})

// API 基础 URL
const apiUrl = 'https://ofun-email-system.htmljs.workers.dev/api'

// 获取认证头
const getAuthHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${props.authToken}`
})

// 格式化日期
const formatDate = (dateStr: string) => {
  return formatDateToChinaTime(dateStr, 'YYYY-MM-DD HH:mm')
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    info: '信息',
    success: '成功',
    warning: '警告',
    error: '错误'
  }
  return typeMap[type] || '信息'
}

// 获取状态类型
const getStatusType = (row: any) => {
  if (!row.is_active) return 'info'
  if (row.expires_at && new Date(row.expires_at) <= new Date()) return 'warning'
  return 'success'
}

// 获取状态文本
const getStatusText = (row: any) => {
  if (!row.is_active) return '已禁用'
  if (row.expires_at && new Date(row.expires_at) <= new Date()) return '已过期'
  return '正常'
}

// 获取公告列表
const fetchAnnouncements = async () => {
  loading.value = true
  try {
    const response = await fetch(`${apiUrl}/admin/announcements`, {
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      announcements.value = result.announcements || []
    } else {
      ElMessage.error(result.message || '获取公告列表失败')
    }
  } catch (error) {
    console.error('Fetch announcements error:', error)
    ElMessage.error('获取公告列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新公告列表
const refreshAnnouncements = () => {
  fetchAnnouncements()
}

// 显示创建对话框
const showCreateDialog = () => {
  announcementDialog.value = {
    visible: true,
    loading: false,
    isEdit: false,
    editId: null,
    form: {
      title: '',
      content: '',
      type: 'info',
      priority: 0,
      expiresInDays: null,
      isActive: true
    }
  }
}

// 编辑公告
const editAnnouncement = (announcement: any) => {
  announcementDialog.value = {
    visible: true,
    loading: false,
    isEdit: true,
    editId: announcement.id,
    form: {
      title: announcement.title,
      content: announcement.content,
      type: announcement.type,
      priority: announcement.priority,
      expiresInDays: announcement.expires_at ? 
        Math.ceil((new Date(announcement.expires_at).getTime() - Date.now()) / (24 * 60 * 60 * 1000)) : 
        null,
      isActive: announcement.is_active
    }
  }
}

// 确认保存
const confirmSave = async () => {
  const form = announcementDialog.value.form
  
  if (!form.title || !form.content) {
    ElMessage.warning('请填写标题和内容')
    return
  }

  announcementDialog.value.loading = true
  try {
    const url = announcementDialog.value.isEdit 
      ? `${apiUrl}/admin/announcements`
      : `${apiUrl}/admin/announcements`
    
    const method = announcementDialog.value.isEdit ? 'PUT' : 'POST'
    
    const body = announcementDialog.value.isEdit 
      ? { ...form, id: announcementDialog.value.editId }
      : form

    const response = await fetch(url, {
      method,
      headers: getAuthHeaders(),
      body: JSON.stringify(body)
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success(announcementDialog.value.isEdit ? '公告更新成功' : '公告创建成功')
      announcementDialog.value.visible = false
      refreshAnnouncements()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('Save announcement error:', error)
    ElMessage.error('操作失败')
  } finally {
    announcementDialog.value.loading = false
  }
}

// 切换状态
const toggleStatus = async (announcement: any) => {
  const newStatus = !announcement.is_active
  const action = newStatus ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(`确定要${action}公告 "${announcement.title}" 吗？`, `${action}公告`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await fetch(`${apiUrl}/admin/announcements`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        id: announcement.id,
        title: announcement.title,
        content: announcement.content,
        type: announcement.type,
        priority: announcement.priority,
        isActive: newStatus,
        expiresInDays: announcement.expires_at ? 
          Math.ceil((new Date(announcement.expires_at).getTime() - Date.now()) / (24 * 60 * 60 * 1000)) : 
          null
      })
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success(`公告${action}成功`)
      refreshAnnouncements()
    } else {
      ElMessage.error(result.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Toggle announcement status error:', error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除公告
const deleteAnnouncement = async (announcement: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除公告 "${announcement.title}" 吗？此操作不可恢复！`, '删除公告', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error'
    })

    const response = await fetch(`${apiUrl}/admin/announcements?id=${announcement.id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    })

    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('公告删除成功')
      refreshAnnouncements()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete announcement error:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 组件挂载时
onMounted(() => {
  fetchAnnouncements()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
