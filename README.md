# 🚀 临时邮箱系统

基于 Cloudflare Workers 和 Vue.js 的现代化临时邮箱系统，支持实时邮件接收和推送。

## ✨ 功能特性

- 🚀 **实时邮件接收**: 基于 Cloudflare 邮件路由的即时邮件处理
- 🔄 **自动刷新**: 智能轮询机制，30秒自动检查新邮件
- 📧 **多域名支持**: 支持多个域名的临时邮箱生成
- 🎯 **验证码提取**: 自动识别和提取6位数字验证码
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 🛡️ **安全机制**: 基于授权密钥的安全访问控制
- 📊 **管理后台**: 完整的邮箱和邮件管理功能
- ⚡ **高性能**: 基于 Cloudflare Workers 的边缘计算

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd temp-mail-app

# 安装依赖
npm install

# 安装 Wrangler CLI
npm install -g wrangler
```

### 2. Cloudflare 配置
```bash
# 登录 Cloudflare
wrangler login

# 部署 Workers
./deploy-workers.sh
```

### 3. 邮件路由配置
- 在 Cloudflare 控制台配置 MX 记录
- 设置 Catch-All 规则指向 `ofun-email-handler`
- 详细步骤请参考 `REALTIME_EMAIL_GUIDE.md`

### 4. 启动应用
```bash
# 开发模式
npm run dev

# 生产构建
npm run build
```

## 📧 实时邮件功能

### 自动刷新机制
- **智能检查**: 先检查邮件数量变化，避免不必要的完整请求
- **实时通知**: 收到新邮件时立即显示通知
- **错误处理**: 网络异常时自动降级处理
- **用户控制**: 可手动启停自动刷新功能

### 使用方法
1. 输入授权密钥查询邮箱
2. 点击"启用自动刷新"开始实时监控
3. 系统每30秒自动检查新邮件
4. 收到新邮件时会显示通知消息

## 🧪 测试功能

打开 `test-realtime-email.html` 进行完整的实时邮件功能测试：
- 邮箱查询和验证
- 自动刷新启停控制
- 实时邮件接收展示
- 系统状态监控

## 🛠️ 开发须知

**技术栈**: Vue 3 + Cloudflare Workers + D1 数据库

### Workers 部署
```bash
# 邮件处理器
wrangler deploy --config wrangler-email.toml

# API 服务
wrangler deploy --config wrangler-api.toml
```

### 数据库迁移
```bash
wrangler d1 execute ofun-email-db --file migrations/0001_initial.sql
```

## 🐛 问题修复

### 邮件接收错误修复
修复了 `message.text is not a function` 错误：
- 使用正确的 Cloudflare 邮件路由 API
- 通过 `message.raw` 流读取邮件内容
- 添加了 MIME 内容解析功能

### 测试邮件接收
```bash
# 启动日志监控
wrangler tail ofun-email-handler

# 或运行测试脚本
./test-email-worker.sh
```

## 📚 文档

- [实时邮件配置指南](REALTIME_EMAIL_GUIDE.md) - 详细的配置步骤
- [测试页面](test-realtime-email.html) - 功能测试和演示
- [邮件发送测试](test-email-send.js) - Node.js 邮件发送脚本