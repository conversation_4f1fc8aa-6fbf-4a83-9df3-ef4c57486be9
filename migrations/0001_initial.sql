-- 创建管理员表
CREATE TABLE IF NOT EXISTS admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建邮箱表
CREATE TABLE IF NOT EXISTS emails (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mail TEXT NOT NULL,
    domain TEXT NOT NULL,
    type TEXT NOT NULL,
    relation TEXT,  -- 绑定关系可以为空
    secret TEXT UNIQUE NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expire_at DATETIME NOT NULL
);

-- 创建邮件表
CREATE TABLE IF NOT EXISTS mail_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email_id INTEGER NOT NULL,
    message_id TEXT UNIQUE NOT NULL,
    from_address TEXT NOT NULL,
    to_address TEXT NOT NULL,
    subject TEXT,
    text_content TEXT,
    html_content TEXT,
    received_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (email_id) REFERENCES emails (id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_admins_username ON admins (username);
CREATE INDEX IF NOT EXISTS idx_emails_secret ON emails (secret);
CREATE INDEX IF NOT EXISTS idx_emails_expire ON emails (expire_at);
CREATE INDEX IF NOT EXISTS idx_emails_domain ON emails (domain);
CREATE INDEX IF NOT EXISTS idx_mail_messages_email_id ON mail_messages (email_id);
CREATE INDEX IF NOT EXISTS idx_mail_messages_received_at ON mail_messages (received_at);

-- 插入默认管理员账户 (用户名: admin, 密码: admin123)
INSERT OR IGNORE INTO admins (username, password) VALUES
('admin', 'admin123');

-- 插入示例邮箱数据
INSERT OR IGNORE INTO emails (mail, domain, type, relation, secret, expire_at) VALUES
('test_123', 'ofun.my', '闲鱼', '81015461774329', 'asyb-123Bsykd-1nat9i', datetime('now', '+30 days')),
('test_456', 'ofun.io', '淘宝', '81015461774329', 'asyb-45113Bsykd-1nat9i', datetime('now', '+30 days'));
