-- 用户系统数据库迁移
-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE,
    password_hash TEXT,
    github_id TEXT UNIQUE,
    avatar_url TEXT,
    display_name TEXT,
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token TEXT,
    reset_token TEXT,
    reset_token_expires DATETIME,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'deleted')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_at DATETIME
);

-- 创建用户积分表
CREATE TABLE IF NOT EXISTS user_credits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    balance INTEGER DEFAULT 0,
    total_earned INTEGER DEFAULT 0,
    total_spent INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建积分交易记录表
CREATE TABLE IF NOT EXISTS credit_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('earn', 'spend', 'refund', 'admin_adjust')),
    amount INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    description TEXT NOT NULL,
    reference_type TEXT, -- 'registration', 'redemption', 'email_generation', 'purchase', 'admin'
    reference_id TEXT,
    metadata TEXT, -- JSON 格式的额外信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建兑换码表
CREATE TABLE IF NOT EXISTS redemption_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    credits INTEGER NOT NULL,
    description TEXT,
    max_uses INTEGER DEFAULT 1,
    used_count INTEGER DEFAULT 0,
    expires_at DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER, -- 管理员ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建兑换码使用记录表
CREATE TABLE IF NOT EXISTS redemption_code_uses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    credits_awarded INTEGER NOT NULL,
    used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(code_id, user_id) -- 防止同一用户多次使用同一兑换码
);

-- 创建用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    session_token TEXT UNIQUE NOT NULL,
    refresh_token TEXT UNIQUE,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address TEXT,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE
);

-- 创建系统公告表
CREATE TABLE IF NOT EXISTS system_announcements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success', 'error')),
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 0,
    target_users TEXT DEFAULT 'all', -- 'all', 'registered', 'new' 或特定用户ID列表
    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME
);

-- 修改邮箱表，添加用户关联
ALTER TABLE emails ADD COLUMN user_id INTEGER;
ALTER TABLE emails ADD COLUMN generated_by_user BOOLEAN DEFAULT FALSE;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users (email);
CREATE INDEX IF NOT EXISTS idx_users_github_id ON users (github_id);
CREATE INDEX IF NOT EXISTS idx_users_status ON users (status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users (created_at);

CREATE INDEX IF NOT EXISTS idx_user_credits_user_id ON user_credits (user_id);

CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_id ON credit_transactions (user_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_type ON credit_transactions (type);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_created_at ON credit_transactions (created_at);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_reference ON credit_transactions (reference_type, reference_id);

CREATE INDEX IF NOT EXISTS idx_redemption_codes_code ON redemption_codes (code);
CREATE INDEX IF NOT EXISTS idx_redemption_codes_active ON redemption_codes (is_active);
CREATE INDEX IF NOT EXISTS idx_redemption_codes_expires ON redemption_codes (expires_at);

CREATE INDEX IF NOT EXISTS idx_redemption_code_uses_code_id ON redemption_code_uses (code_id);
CREATE INDEX IF NOT EXISTS idx_redemption_code_uses_user_id ON redemption_code_uses (user_id);

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions (user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions (session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions (expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions (is_active);

CREATE INDEX IF NOT EXISTS idx_system_announcements_active ON system_announcements (is_active);
CREATE INDEX IF NOT EXISTS idx_system_announcements_priority ON system_announcements (priority);
CREATE INDEX IF NOT EXISTS idx_system_announcements_expires ON system_announcements (expires_at);

CREATE INDEX IF NOT EXISTS idx_emails_user_id ON emails (user_id);
CREATE INDEX IF NOT EXISTS idx_emails_generated_by_user ON emails (generated_by_user);
