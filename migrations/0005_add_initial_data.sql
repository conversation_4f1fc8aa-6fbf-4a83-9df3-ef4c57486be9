-- 初始数据迁移
-- 插入系统公告示例
INSERT OR IGNORE INTO system_announcements (title, content, type, is_active, priority, target_users, created_by) VALUES
('欢迎使用临时邮箱系统', '欢迎使用我们的临时邮箱系统！新用户注册即可获得5个免费配额。', 'success', 1, 1, 'all', 1),
('配额使用说明', '每生成一个临时邮箱需要消耗1个配额。您可以通过兑换码或购买来获取更多配额。', 'info', 1, 0, 'registered', 1);

-- 插入示例兑换码（仅用于测试）
INSERT OR IGNORE INTO redemption_codes (code, credits, description, max_uses, expires_at, created_by) VALUES
('WELCOME2024', 10, '新用户欢迎礼包', 100, datetime('now', '+30 days'), 1),
('TESTCODE123', 5, '测试兑换码', 50, datetime('now', '+7 days'), 1);

-- 创建触发器：用户注册时自动创建积分记录并赠送5积分
CREATE TRIGGER IF NOT EXISTS create_user_credits_on_register
AFTER INSERT ON users
BEGIN
    -- 创建用户积分记录
    INSERT INTO user_credits (user_id, balance, total_earned)
    VALUES (NEW.id, 5, 5);
    
    -- 记录注册赠送积分的交易
    INSERT INTO credit_transactions (user_id, type, amount, balance_after, description, reference_type, reference_id)
    VALUES (NEW.id, 'earn', 5, 5, '新用户注册奖励', 'registration', NEW.id);
END;

-- 创建触发器：更新用户积分时自动更新 updated_at
CREATE TRIGGER IF NOT EXISTS update_user_credits_timestamp
AFTER UPDATE ON user_credits
BEGIN
    UPDATE user_credits SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 创建触发器：更新用户信息时自动更新 updated_at
CREATE TRIGGER IF NOT EXISTS update_users_timestamp
AFTER UPDATE ON users
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 创建触发器：更新兑换码时自动更新 updated_at
CREATE TRIGGER IF NOT EXISTS update_redemption_codes_timestamp
AFTER UPDATE ON redemption_codes
BEGIN
    UPDATE redemption_codes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 创建触发器：更新系统公告时自动更新 updated_at
CREATE TRIGGER IF NOT EXISTS update_system_announcements_timestamp
AFTER UPDATE ON system_announcements
BEGIN
    UPDATE system_announcements SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 创建触发器：用户会话使用时更新 last_used_at
CREATE TRIGGER IF NOT EXISTS update_session_last_used
AFTER UPDATE ON user_sessions
WHEN NEW.last_used_at = OLD.last_used_at
BEGIN
    UPDATE user_sessions SET last_used_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
