-- 添加邮箱验证表
CREATE TABLE IF NOT EXISTS email_verifications (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  email TEXT NOT NULL,
  code TEXT NOT NULL,
  expires_at DATETIME NOT NULL,
  used INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 为邮箱验证表创建索引
CREATE INDEX IF NOT EXISTS idx_email_verifications_email ON email_verifications(email);
CREATE INDEX IF NOT EXISTS idx_email_verifications_code ON email_verifications(code);
CREATE INDEX IF NOT EXISTS idx_email_verifications_expires ON email_verifications(expires_at);

-- 为用户表添加GitHub相关字段（如果不存在）
ALTER TABLE users ADD COLUMN github_id TEXT;
ALTER TABLE users ADD COLUMN password_hash TEXT;

-- 为GitHub ID创建索引
CREATE INDEX IF NOT EXISTS idx_users_github_id ON users(github_id);
